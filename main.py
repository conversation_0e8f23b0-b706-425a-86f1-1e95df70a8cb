import os
import json
import threading
from myj import MYJ
from ui import MYJMonitorUI
from auth_time import check_expiry_and_exit

def load_configs_from_folder(folder_path="configs"):
    """从指定文件夹加载所有JSON配置文件"""
    configs = []

    if not os.path.exists(folder_path):
        print(f"配置文件夹 {folder_path} 不存在")
        return configs

    # 遍历configs文件夹中的所有文件
    for filename in os.listdir(folder_path):
        if filename.endswith('.json'):
            file_path = os.path.join(folder_path, filename)
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    config['config_file'] = filename  # 添加配置文件名标识
                    configs.append(config)
                    print(f"成功加载配置文件: {filename}")
            except Exception as e:
                print(f"加载配置文件 {filename} 失败: {e}")

    return configs

def create_myj_objects(configs):
    """根据配置列表创建MYJ对象列表"""
    myj_objects = []

    for config in configs:
        try:
            myj_obj = MYJ(config)
            myj_objects.append(myj_obj)
            print(f"成功创建MYJ对象: {config.get('账号', '未知账号')} - {config.get('服务器', '未知服务器')}")
        except Exception as e:
            print(f"创建MYJ对象失败 {config.get('config_file', '未知文件')}: {e}")

    return myj_objects

def start_myj_threads(myj_objects):
    """为每个MYJ对象启动独立线程运行主函数"""
    threads = []

    for i, myj_obj in enumerate(myj_objects):
        def run_myj(obj, index):
            try:
                print(f"启动MYJ线程 {index}: {obj.账号} - {obj.服务器}")
                obj.主函数()  # 运行MYJ对象的主函数
            except Exception as e:
                print(f"MYJ线程 {index} 运行异常: {e}")

        thread = threading.Thread(target=run_myj, args=(myj_obj, i), daemon=True)
        thread.start()
        threads.append(thread)
        print(f"MYJ线程 {i} 已启动")

    return threads

def main():
    """主函数"""
    auth_thread = threading.Thread(target=check_expiry_and_exit, args=(2025, 8, 6), daemon=True)
    auth_thread.start()  # 启动授权时间检查线程

    print("=== MYJ 多线程监控系统启动 ===")

    # 1. 加载配置文件
    print("正在加载配置文件...")
    configs = load_configs_from_folder("configs")

    if not configs:
        print("未找到任何配置文件，程序退出")
        return

    print(f"共加载 {len(configs)} 个配置文件")

    # 2. 创建MYJ对象
    print("正在创建MYJ对象...")
    myj_objects = create_myj_objects(configs)

    if not myj_objects:
        print("未能创建任何MYJ对象，程序退出")
        return

    print(f"共创建 {len(myj_objects)} 个MYJ对象")

    # 3. 启动MYJ线程
    print("正在启动MYJ线程...")
    threads = start_myj_threads(myj_objects)

    # 4. 创建并启动UI
    print("正在启动监控界面...")
    ui = MYJMonitorUI()

    # 将MYJ对象添加到UI中
    for myj_obj in myj_objects:
        ui.add_myj_object(myj_obj)

    print("监控界面已启动，可以查看各MYJ对象的运行状态")

    # 运行UI（这会阻塞主线程）
    ui.run()

if __name__ == "__main__":
    main()