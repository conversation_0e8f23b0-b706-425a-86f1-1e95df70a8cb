import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
import datetime

def send_qq_email(receiver: str, content: str):
    smtp_server = "smtp.qq.com"
    sender_email = "<EMAIL>"
    # 构建邮件对象
    msg = MIMEMultipart()
    msg['From'] = '<EMAIL>'
    msg["To"] = receiver
    msg["Subject"] = '小叮当-极-提醒邮件'
    body = f'{(datetime.datetime.now()+ datetime.timedelta(days=-1)).strftime("%Y-%m-%d")}\n{content}'
    msg.attach(MIMEText(body, 'plain'))
    server = smtplib.SMTP(smtp_server, 587)
    server.starttls()
    server.login(sender_email, 'eidkyaigsbgdbabg')
    server.sendmail(sender_email, receiver, msg.as_string())
    server.quit()
    

def send_bug_email(content: str):
    send_qq_email('<EMAIL>', content)
    
if __name__ == '__main__':
    send_bug_email('测试邮件')