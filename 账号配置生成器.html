<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>账号配置生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 25px;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 3px;
        }
        .section h3 {
            margin-top: 0;
            color: #555;
            border-bottom: 2px solid #eee;
            padding-bottom: 5px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: inline-block;
            width: 150px;
            font-weight: bold;
            color: #666;
            white-space: nowrap;
        }
        input[type="text"], input[type="password"], select, textarea {
            width: 300px;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
            font-size: 14px;
        }
        input[type="checkbox"] {
            margin-right: 5px;
        }
        textarea {
            height: 60px;
            resize: vertical;
        }
        .skill-item {
            background-color: #f9f9f9;
            padding: 10px;
            margin: 5px 0;
            border-radius: 3px;
        }
        .skill-item input {
            width: 80px;
            margin-right: 10px;
        }
        .buttons {
            text-align: center;
            margin-top: 30px;
        }
        button {
            padding: 10px 20px;
            margin: 0 10px;
            font-size: 16px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .btn-generate {
            background-color: #4CAF50;
            color: white;
        }
        .btn-generate:hover {
            background-color: #45a049;
        }
        .btn-clear {
            background-color: #f44336;
            color: white;
        }
        .btn-clear:hover {
            background-color: #da190b;
        }
        #output {
            margin-top: 20px;
            padding: 15px;
            background-color: #f0f0f0;
            border: 1px solid #ccc;
            border-radius: 3px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .array-input {
            width: 400px;
        }
        .editable-select {
            position: relative;
        }
        .editable-select input {
            width: 100%;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .editable-select .dropdown-arrow {
            position: absolute;
            right: 10px;
            top: 8px;
            cursor: pointer;
            font-size: 12px;
            color: #666;
            line-height: 20px;
            border: none;
            background: none;
        }
        .editable-select .dropdown-list {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ccc;
            border-top: none;
            border-radius: 0 0 3px 3px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }
        .editable-select .dropdown-item {
            padding: 8px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
        }
        .editable-select .dropdown-item:hover {
            background-color: #f5f5f5;
        }
        .editable-select .dropdown-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>账号配置生成器</h1>
        
        <!-- 基础配置 -->
        <div class="section">
            <h3>基础配置</h3>
            <div class="form-group">
                <label>服务器:</label>
                <select id="server">
                    <option value="x12" selected>x12</option>
                    <option value="x39">x39</option>
                    <option value="s69">s69</option>
                    <option value="s91">s91</option>
                    <option value="s92">s92</option>
                    <option value="s93">s93</option>
                    <option value="s96">s96</option>
                    <option value="s97">s97</option>
                </select>
            </div>
            <div class="form-group">
                <label>账号:</label>
                <input type="text" id="account" value="">
            </div>
            <div class="form-group">
                <label>密码:</label>
                <input type="password" id="password" value="">
            </div>
            <div class="form-group">
                <label>角色序号:</label>
                <select id="roleIndex">
                    <option value="0" selected>0</option>
                    <option value="1">1</option>
                    <option value="2">2</option>
                    <option value="3">3</option>
                    <option value="4">4</option>
                </select>
            </div>
        </div>

        <!-- 代理配置 -->
        <div class="section">
            <h3>代理配置</h3>
            <div class="form-group">
                <label>代理账号:</label>
                <input type="text" id="proxyAccount" value="">
            </div>
            <div class="form-group">
                <label>代理密码:</label>
                <input type="text" id="proxyPassword" value="">
            </div>
        </div>

        <!-- 任务开关 -->
        <div class="section">
            <h3>任务开关</h3>
            <div class="form-group">
                <label><input type="checkbox" id="dailyTuoba" checked>日常拖把开关</label>
            </div>
            <div class="form-group">
                <label><input type="checkbox" id="dailyMohua">日常魔化开关</label>
            </div>
            <div class="form-group">
                <label><input type="checkbox" id="activityRuyi">活动如意开关</label>
            </div>
            <div class="form-group">
                <label><input type="checkbox" id="activityTongtian">活动通天开关(待测试)</label>
            </div>
            <div class="form-group">
                <label><input type="checkbox" id="activityNiwushuang">活动逆无双开关(待测试)</label>
            </div>
            <div class="form-group">
                <label><input type="checkbox" id="activityLuohan">活动罗汉开关(暂未实装)</label>
            </div>
        </div>

        <!-- 闲时任务 -->
        <div class="section">
            <h3>闲时任务</h3>
            <div class="form-group">
                <label>闲时任务:</label>
                <select id="idleTask">
                    <option value="闲时_禅壹" selected>闲时_禅壹</option>
                    <option value="闲时_禅伍">闲时_禅伍</option>
                    <option value="闲时_道壹">闲时_道壹</option>
                    <option value="闲时_打工">闲时_打工</option>
                    <option value="闲时_刷怪">闲时_刷怪</option>
                </select>
            </div>
            <div class="form-group">
                <label>换装包前缀:</label>
                <input type="text" id="equipPrefix" value="">
            </div>
            <div class="form-group">
                <label>经验百分比上限(暂时不可用):</label>
                <input type="text" id="levelLimit" value="100" placeholder="输入0-100的数字，或留空表示不限制">
            </div>
        </div>

        <!-- 通天配置 -->
        <div class="section">
            <h3>通天配置</h3>
            <div class="form-group">
                <label>刷怪武器:</label>
                <input type="text" id="tongtianWeapon" value="">
            </div>
            <div class="form-group">
                <label>职业技能:</label>
                <select id="tongtianSkill">
                    <option value="龙葵匕刃">龙葵匕刃</option>
                    <option value="十字剑" selected>十字剑</option>
                    <option value="幽冥爪">幽冥爪</option>
                    <option value="霸王枪">霸王枪</option>
                    <option value="飓风刀法">飓风刀法</option>
                    <option value="冰系法术">冰系法术</option>
                    <option value="暗魔法">暗魔法</option>
                    <option value="火系法术">火系法术</option>
                    <option value="圣魔法">圣魔法</option>
                    <option value="风系法术">风系法术</option>
                </select>
            </div>
            <div class="form-group">
                <label>释放技能列表:</label>
                <textarea id="tongtianSkillList" class="array-input">猛抽,致命一击,强力打击[1级]</textarea>
            </div>
        </div>

        <!-- 逆无双配置 -->
        <div class="section">
            <h3>逆无双配置</h3>
            <div class="form-group">
                <label>刷怪武器:</label>
                <input type="text" id="niwushuangWeapon" value="">
            </div>
            <div class="form-group">
                <label>职业技能:</label>
                <select id="niwushuangSkill">
                    <option value="龙葵匕刃">龙葵匕刃</option>
                    <option value="十字剑" selected>十字剑</option>
                    <option value="幽冥爪">幽冥爪</option>
                    <option value="霸王枪">霸王枪</option>
                    <option value="飓风刀法">飓风刀法</option>
                    <option value="冰系法术">冰系法术</option>
                    <option value="暗魔法">暗魔法</option>
                    <option value="火系法术">火系法术</option>
                    <option value="圣魔法">圣魔法</option>
                    <option value="风系法术">风系法术</option>
                </select>
            </div>
            <div class="form-group">
                <label>释放技能列表:</label>
                <textarea id="niwushuangSkillList" class="array-input">猛抽,致命一击,强力打击[1级]</textarea>
            </div>
        </div>

        <!-- 刷怪配置 -->
        <div class="section">
            <h3>刷怪配置</h3>
            <div class="form-group">
                <label>刷怪武器:</label>
                <input type="text" id="brushWeapon" value="">
            </div>
            <div class="form-group">
                <label>职业技能:</label>
                <select id="brushSkill">
                    <option value="龙葵匕刃" selected>龙葵匕刃</option>
                    <option value="十字剑">十字剑</option>
                    <option value="幽冥爪">幽冥爪</option>
                    <option value="霸王枪">霸王枪</option>
                    <option value="飓风刀法">飓风刀法</option>
                    <option value="冰系法术">冰系法术</option>
                    <option value="暗魔法">暗魔法</option>
                    <option value="火系法术">火系法术</option>
                    <option value="圣魔法">圣魔法</option>
                    <option value="风系法术">风系法术</option>
                </select>
            </div>
            <div class="form-group">
                <label>释放技能列表:</label>
                <textarea id="brushSkillList" class="array-input">强力打击[1级],凿击[1级],猛抽,致命一击</textarea>
            </div>
            <div class="form-group">
                <label>刷怪起点:</label>
                <div class="editable-select">
                    <input type="text" id="brushStart" value="map.kasitepingyuan|卡斯特平原34" placeholder="例如: map.kasitepingyuan|卡斯特平原34">
                    <button type="button" class="dropdown-arrow" onclick="toggleDropdown('brushStart')" title="点击展开选项">▼</button>
                    <div class="dropdown-list" id="brushStart_dropdown">
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.baimagang|白马港驿站')">map.baimagang|白马港驿站</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.baimahekou|白马河口01')">map.baimahekou|白马河口01</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.bingfengpubu|冰封瀑布19')">map.bingfengpubu|冰封瀑布19</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.bingfengwan|冰封湾13')">map.bingfengwan|冰封湾13</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.bingheliegu|冰河裂谷02')">map.bingheliegu|冰河裂谷02</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.bingxuecheng|广场')">map.bingxuecheng|广场</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.daxueshan|西坡_00')">map.daxueshan|西坡_00</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.diailindi|矮林边界')">map.diailindi|矮林边界</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.dongtutaiyuan|冻土苔原03')">map.dongtutaiyuan|冻土苔原03</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.gaiyazhicheng|盖亚之城驿站')">map.gaiyazhicheng|盖亚之城驿站</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.hanfenggu|寒风谷')">map.hanfenggu|寒风谷</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.jingling.city|驿站')">map.jingling.city|驿站</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.jingling|南部森林20')">map.jingling|南部森林20</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.jinglingzhisen|精灵之森50')">map.jinglingzhisen|精灵之森50</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.jinshawan|金沙湾00')">map.jinshawan|金沙湾00</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.jixiedao|机械岛出口')">map.jixiedao|机械岛出口</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.juduzhaoze|毒水源01')">map.juduzhaoze|毒水源01</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.kasitepingyuan|卡斯特平原34')">map.kasitepingyuan|卡斯特平原34</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.longjishan|龙脊山25')">map.longjishan|龙脊山25</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.longhehegu|龙河河谷00')">map.longhehegu|龙河河谷00</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.luweidang|芦苇荡19')">map.luweidang|芦苇荡19</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.maoyin|村口')">map.maoyin|村口</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.maoyinwest|西村口')">map.maoyinwest|西村口</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.maoyougongzuoshi|果果堂')">map.maoyougongzuoshi|果果堂</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.mawangtan|马王滩00')">map.mawangtan|马王滩00</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.mopcity|驿站')">map.mopcity|驿站</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.muyecaoyuan|牧野草原03')">map.muyecaoyuan|牧野草原03</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.qingquanhu|清泉湖52')">map.qingquanhu|清泉湖52</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.qingquanpubu|清泉瀑布00')">map.qingquanpubu|清泉瀑布00</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.shuijingdao|出口')">map.shuijingdao|出口</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.tongchi|通吃岛渡口')">map.tongchi|通吃岛渡口</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.wanmayuan|万马草原_52')">map.wanmayuan|万马草原_52</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.wuminghu|无名湖27')">map.wuminghu|无名湖27</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.wuyingdao|雾影岛渡口')">map.wuyingdao|雾影岛渡口</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.xisaipingyuan|西赛村驿站')">map.xisaipingyuan|西赛村驿站</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.xiongmaosenlin|熊猫森林15')">map.xiongmaosenlin|熊猫森林15</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.xiuluogong|武器店')">map.xiuluogong|武器店</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.xiwangmatou|驿站')">map.xiwangmatou|驿站</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.xuesongsenlin|雪松森林_00')">map.xuesongsenlin|雪松森林_00</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.xueyuancheng|雪原城驿站')">map.xueyuancheng|雪原城驿站</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.yinguangsenlin|银光森林30')">map.yinguangsenlin|银光森林30</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.yinyue|银月广场')">map.yinyue|银月广场</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.yinxuepingyuan|银雪平原22')">map.yinxuepingyuan|银雪平原22</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.yuansudao|元素岛石碑01')">map.yuansudao|元素岛石碑01</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.yunmengze1|云梦泽107')">map.yunmengze1|云梦泽107</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.yunmengze2|云梦泽206')">map.yunmengze2|云梦泽206</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.yunmengze3|云梦泽331')">map.yunmengze3|云梦泽331</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.yunmengze4|云梦泽422')">map.yunmengze4|云梦泽422</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.yunmengze5|云梦泽535')">map.yunmengze5|云梦泽535</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.yunmengze6|云梦泽641')">map.yunmengze6|云梦泽641</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.yunmengze7|云梦泽700')">map.yunmengze7|云梦泽700</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.yunyancheng|云岩城驿站')">map.yunyancheng|云岩城驿站</div>
                        <div class="dropdown-item" onclick="selectOption('brushStart', 'map.yunyandao|元素岛')">map.yunyandao|元素岛</div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label>刷怪路线:</label>
                <textarea id="brushRoute" class="array-input">卡斯特平原34,卡斯特平原12,卡斯特平原15,卡斯特平原41,卡斯特平原19,卡斯特平原42,卡斯特平原21,卡斯特平原17,卡斯特平原18,卡斯特平原16,卡斯特平原36,卡斯特平原35</textarea>
            </div>
            <div class="form-group">
                <label>刷怪目标:</label>
                <textarea id="brushTargets" class="array-input">豆芽,蚱蜢,螃蟹</textarea>
            </div>
            <div class="form-group">
                <label>伏击技能:</label>
                <select id="ambushSkill">
                    <option value="伏击[一级]" selected>伏击[一级]</option><option value="伏击[二级]">伏击[二级]</option><option value="伏击[三级]">伏击[三级]</option><option value="伏击[四级]">伏击[四级]</option><option value="伏击[五级]">伏击[五级]</option><option value="伏击[六级]">伏击[六级]</option><option value="伏击[七级]">伏击[七级]</option><option value="伏击[八级]">伏击[八级]</option><option value="伏击[九级]">伏击[九级]</option>
                </select>
            </div>
            <div class="form-group">
                <label>售卖圈数:</label>
                <input type="text" id="sellCycles" value="200">
            </div>
            <div class="form-group">
                <label>售卖道具列表:</label>
                <textarea id="sellItems" class="array-input">猎鹰之,巨猿之,野猪之,猛虎之,达拉,宝箱,民兵,针织,螃蟹,亚麻,普通,农夫,园艺,能量之,敏捷之,鼓舞,坚韧,精神,奥术,猛击,炸弹,战斗怒吼,暴怒,残忍,雄鹰之,灵猴之,巨鲸之</textarea>
            </div>
            <div class="form-group">
                <label>每圈结束执行其它代码:</label>
                <div class="editable-select">
                    <input type="text" id="otherCode" placeholder="选择饲料类型或自定义命令">
                    <button type="button" class="dropdown-arrow" onclick="toggleDropdown('otherCode')" title="点击展开选项">▼</button>
                    <div class="dropdown-list" id="otherCode_dropdown">
                        <div class="dropdown-item" onclick="selectOption('otherCode', '')">不执行其它代码</div>
                        <div class="dropdown-item" onclick="selectOption('otherCode', 'use fitems.shouhuling.other.ChuJiSiLiao')">低级守护灵饲料</div>
                        <div class="dropdown-item" onclick="selectOption('otherCode', 'use fitems.shouhuling.other.ZhongJiSiLiao')">中级守护灵饲料</div>
                        <div class="dropdown-item" onclick="selectOption('otherCode', 'use fitems.shouhuling.other.GaoJiSiLiao')">高级守护灵饲料</div>
                    </div>
                </div>
                <div style="margin-top: 5px; font-size: 12px; color: #666;">
                    注意：此命令将在每完成一轮刷怪后执行
                </div>
            </div>
        </div>

        <!-- 技能配置 -->
        <div class="section">
            <h3>技能配置</h3>
            <div class="skill-config" style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 3px; background-color: #f9f9f9;">
                <div style="margin-bottom: 10px; font-weight: bold;">
                    <span style="display: inline-block; width: 30%;">技能名称</span>
                    <span style="display: inline-block; width: 20%;">前摇(秒)</span>
                    <span style="display: inline-block; width: 20%;">后摇(秒)</span>
                    <span style="display: inline-block; width: 20%;">CD(秒)</span>
                </div>
                <div id="skillsContainer">
                    <!-- 技能项目将通过JavaScript动态生成 -->
                </div>
                <button type="button" onclick="addSkill()" class="btn-secondary" style="background-color: #6c757d; color: white; padding: 8px 16px; border: none; border-radius: 3px; cursor: pointer; margin-top: 10px;">添加技能</button>
            </div>
        </div>

        <!-- 按钮 -->
        <div class="buttons">
            <button class="btn-generate" onclick="generateConfig()">生成配置</button>
            <button class="btn-clear" onclick="clearOutput()">清空输出</button>
            <button class="btn-download" onclick="downloadConfig()" style="background-color: #28a745; color: white;">下载配置</button>
            <button class="btn-load" onclick="loadConfig()" style="background-color: #17a2b8; color: white;">读取配置</button>
        </div>

        <!-- 输出区域 -->
        <div id="output"></div>
    </div>

    <script>
        // 默认技能配置
        var defaultSkills = [
            {"name": "出血[6级]", "pre": 0, "post": 0, "cd": 5},
            {"name": "冥火爪[11级]", "pre": 0, "post": 0, "cd": 10},
            {"name": "强力打击[11级]", "pre": 3, "post": 0, "cd": 6},
            {"name": "猛抽", "pre": 0, "post": 0, "cd": 30},
            {"name": "致命一击", "pre": 3, "post": 0, "cd": 40}
        ];

        // 初始化页面
        function initPage() {
            loadDefaultSkills();
            setupClickOutside();
        }

        // 加载默认技能
        function loadDefaultSkills() {
            var skillsDiv = document.getElementById('skillsContainer');
            skillsDiv.innerHTML = '';
            for (var i = 0; i < defaultSkills.length; i++) {
                addSkillItem(defaultSkills[i].name, defaultSkills[i].pre, defaultSkills[i].post, defaultSkills[i].cd);
            }
        }

        // 添加技能项目
        function addSkillItem(name, pre, post, cd) {
            name = name || '';
            pre = pre || 0;
            post = post || 0;
            cd = cd || 0;

            var skillsDiv = document.getElementById('skillsContainer');
            var skillItem = document.createElement('div');
            skillItem.className = 'skill-item';
            skillItem.style.cssText = 'display: table; width: 100%; margin-bottom: 10px;';
            skillItem.innerHTML =
                '<div style="display: table-cell; width: 30%; padding: 5px; vertical-align: middle;"><input type="text" value="' + name + '" placeholder="技能名称" style="width: 90%; padding: 4px;"></div>' +
                '<div style="display: table-cell; width: 20%; padding: 5px; vertical-align: middle;"><input type="number" value="' + pre + '" min="0" step="0.1" style="width: 60px; padding: 4px;"></div>' +
                '<div style="display: table-cell; width: 20%; padding: 5px; vertical-align: middle;"><input type="number" value="' + post + '" min="0" step="0.1" style="width: 60px; padding: 4px;"></div>' +
                '<div style="display: table-cell; width: 20%; padding: 5px; vertical-align: middle;"><input type="number" value="' + cd + '" min="0" step="0.1" style="width: 60px; padding: 4px;"></div>' +
                '<div style="display: table-cell; padding: 5px; vertical-align: middle;"><button type="button" onclick="removeSkill(this)" style="padding: 4px 8px; background-color: #f44336; color: white; border: none; border-radius: 3px; cursor: pointer;">删除</button></div>';
            skillsDiv.appendChild(skillItem);
        }

        // 添加新技能
        function addSkill() {
            addSkillItem('', 0, 0, 0);
        }

        // 删除技能
        function removeSkill(button) {
            button.parentNode.parentNode.parentNode.removeChild(button.parentNode.parentNode);
        }

        // 辅助函数：将空字符串转换为null
        function emptyToNull(value) {
            return (value === null || value === undefined || value.trim() === '') ? null : value;
        }

        function generateConfig() {
            var config = {
                "服务器": document.getElementById('server').value,
                "账号": emptyToNull(document.getElementById('account').value),
                "密码": emptyToNull(document.getElementById('password').value),
                "角色序号": document.getElementById('roleIndex').value,
                "日常拖把开关": document.getElementById('dailyTuoba').checked,
                "日常魔化开关": document.getElementById('dailyMohua').checked,
                "活动如意开关": document.getElementById('activityRuyi').checked,
                "活动通天开关": document.getElementById('activityTongtian').checked,
                "活动逆无双开关": document.getElementById('activityNiwushuang').checked,
                "活动罗汉开关": document.getElementById('activityLuohan').checked,
                "闲时任务": document.getElementById('idleTask').value,
                "换装包前缀": emptyToNull(document.getElementById('equipPrefix').value),
                "卡级配置": (function() {
                    var levelValue = document.getElementById('levelLimit').value.trim();
                    if (levelValue === '' || levelValue === 'null' || levelValue === 'None') {
                        return null;
                    }
                    var num = parseInt(levelValue);
                    return (num >= 0 && num <= 100) ? num : null;
                })(),
                "通天配置": {
                    "刷怪武器": emptyToNull(document.getElementById('tongtianWeapon').value),
                    "职业技能": document.getElementById('tongtianSkill').value,
                    "释放技能列表": document.getElementById('tongtianSkillList').value.split(',').map(function(s) { return s.trim(); }).filter(function(s) { return s !== ''; })
                },
                "逆无双配置": {
                    "刷怪武器": emptyToNull(document.getElementById('niwushuangWeapon').value),
                    "职业技能": document.getElementById('niwushuangSkill').value,
                    "释放技能列表": document.getElementById('niwushuangSkillList').value.split(',').map(function(s) { return s.trim(); }).filter(function(s) { return s !== ''; })
                },
                "刷怪配置": {
                    "刷怪武器": emptyToNull(document.getElementById('brushWeapon').value),
                    "职业技能": document.getElementById('brushSkill').value,
                    "释放技能列表": document.getElementById('brushSkillList').value.split(',').map(function(s) { return s.trim(); }).filter(function(s) { return s !== ''; }),
                    "刷怪起点": document.getElementById('brushStart').value,
                    "刷怪路线": document.getElementById('brushRoute').value,
                    "刷怪目标": document.getElementById('brushTargets').value.split(',').map(function(s) { return s.trim(); }).filter(function(s) { return s !== ''; }),
                    "伏击技能": emptyToNull(document.getElementById('ambushSkill').value),
                    "每圈结束执行的其他代码": emptyToNull(document.getElementById('otherCode').value),
                    "跑图多少圈进行一次售卖": parseInt(document.getElementById('sellCycles').value),
                    "售卖道具列表": document.getElementById('sellItems').value.split(',').map(function(s) { return s.trim(); }).filter(function(s) { return s !== ''; })
                },
                "技能配置": {}
            };

            // 处理代理配置：只有当代理账号或代理密码不为空时才添加代理配置
            var proxyAccount = emptyToNull(document.getElementById('proxyAccount').value);
            var proxyPassword = emptyToNull(document.getElementById('proxyPassword').value);
            if (proxyAccount !== null || proxyPassword !== null) {
                config["代理配置"] = {
                    "代理账号": proxyAccount,
                    "代理密码": proxyPassword
                };
            }

            // 收集技能配置
            var skillItems = document.getElementById('skillsContainer').getElementsByClassName('skill-item');
            for (var i = 0; i < skillItems.length; i++) {
                var inputs = skillItems[i].getElementsByTagName('input');
                var skillName = inputs[0].value.trim();
                if (skillName) {
                    config["技能配置"][skillName] = {
                        "前摇": parseFloat(inputs[1].value) || 0,
                        "后摇": parseFloat(inputs[2].value) || 0,
                        "CD": parseFloat(inputs[3].value) || 0,
                        "剩余CD": 0
                    };
                }
            }

            var jsonString = JSON.stringify(config, null, 4);
            document.getElementById('output').textContent = jsonString;
        }

        function clearOutput() {
            document.getElementById('output').textContent = '';
        }

        // 下载配置文件
        function downloadConfig() {
            var content = document.getElementById('output').textContent;
            if (!content) {
                alert('请先生成配置！');
                return;
            }

            var account = document.getElementById('account').value || 'config';
            var filename = account + '.json';

            if (window.navigator && window.navigator.msSaveBlob) {
                // IE10+
                var blob = new Blob([content], {type: 'application/json'});
                window.navigator.msSaveBlob(blob, filename);
            } else {
                // 其他浏览器
                var element = document.createElement('a');
                element.setAttribute('href', 'data:application/json;charset=utf-8,' + encodeURIComponent(content));
                element.setAttribute('download', filename);
                element.style.display = 'none';
                document.body.appendChild(element);
                element.click();
                document.body.removeChild(element);
            }
        }

        // 读取配置文件
        function loadConfig() {
            var input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = function(event) {
                var file = event.target.files[0];
                if (file) {
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        try {
                            var config = JSON.parse(e.target.result);
                            fillFormWithConfig(config);
                            alert('配置文件读取成功！');
                        } catch (error) {
                            alert('配置文件格式错误：' + error.message);
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }

        // 用配置数据填充表单
        function fillFormWithConfig(config) {
            // 基本信息
            if (config.服务器) document.getElementById('server').value = config.服务器;
            if (config.账号) document.getElementById('account').value = config.账号;
            if (config.密码) document.getElementById('password').value = config.密码;
            if (config.角色序号) document.getElementById('roleIndex').value = config.角色序号;

            // 任务开关
            if (config.日常拖把开关 !== undefined) document.getElementById('dailyTuoba').checked = config.日常拖把开关;
            if (config.日常魔化开关 !== undefined) document.getElementById('dailyMohua').checked = config.日常魔化开关;
            if (config.活动如意开关 !== undefined) document.getElementById('activityRuyi').checked = config.活动如意开关;
            if (config.活动通天开关 !== undefined) document.getElementById('activityTongtian').checked = config.活动通天开关;
            if (config.活动逆无双开关 !== undefined) document.getElementById('activityNiwushuang').checked = config.活动逆无双开关;
            if (config.活动罗汉开关 !== undefined) document.getElementById('activityLuohan').checked = config.活动罗汉开关;

            // 其他配置
            if (config.闲时任务) document.getElementById('idleTask').value = config.闲时任务;
            if (config.换装包前缀) document.getElementById('equipPrefix').value = config.换装包前缀;
            if (config.卡级配置) document.getElementById('levelLimit').value = config.卡级配置;

            // 通天配置
            if (config.通天配置) {
                if (config.通天配置.刷怪武器) document.getElementById('tongtianWeapon').value = config.通天配置.刷怪武器;
                if (config.通天配置.职业技能) document.getElementById('tongtianSkill').value = config.通天配置.职业技能;
                if (config.通天配置.释放技能列表) document.getElementById('tongtianSkillList').value = config.通天配置.释放技能列表.join(',');
            }

            // 逆无双配置
            if (config.逆无双配置) {
                if (config.逆无双配置.刷怪武器) document.getElementById('niwushuangWeapon').value = config.逆无双配置.刷怪武器;
                if (config.逆无双配置.职业技能) document.getElementById('niwushuangSkill').value = config.逆无双配置.职业技能;
                if (config.逆无双配置.释放技能列表) document.getElementById('niwushuangSkillList').value = config.逆无双配置.释放技能列表.join(',');
            }

            // 刷怪配置
            if (config.刷怪配置) {
                if (config.刷怪配置.刷怪武器) document.getElementById('brushWeapon').value = config.刷怪配置.刷怪武器;
                if (config.刷怪配置.职业技能) document.getElementById('brushSkill').value = config.刷怪配置.职业技能;
                if (config.刷怪配置.释放技能列表) document.getElementById('brushSkillList').value = config.刷怪配置.释放技能列表.join(',');
                if (config.刷怪配置.刷怪起点) document.getElementById('brushStart').value = config.刷怪配置.刷怪起点;
                if (config.刷怪配置.刷怪路线) document.getElementById('brushRoute').value = config.刷怪配置.刷怪路线;
                if (config.刷怪配置.刷怪目标) document.getElementById('brushTargets').value = config.刷怪配置.刷怪目标.join(',');
                if (config.刷怪配置.伏击技能) document.getElementById('ambushSkill').value = config.刷怪配置.伏击技能;
                if (config.刷怪配置.每圈结束执行的其他代码) document.getElementById('otherCode').value = config.刷怪配置.每圈结束执行的其他代码;
                if (config.刷怪配置.跑图多少圈进行一次售卖) document.getElementById('sellCycles').value = config.刷怪配置.跑图多少圈进行一次售卖;
                if (config.刷怪配置.售卖道具列表) document.getElementById('sellItems').value = config.刷怪配置.售卖道具列表.join(',');
            }

            // 技能配置
            if (config.技能配置) {
                // 清空现有技能
                document.getElementById('skillsContainer').innerHTML = '';
                // 添加配置中的技能
                for (var skillName in config.技能配置) {
                    var skill = config.技能配置[skillName];
                    addSkillItem(skillName, skill.前摇 || 0, skill.后摇 || 0, skill.CD || 0);
                }
            }
        }

        // 可编辑下拉框功能
        function toggleDropdown(fieldId) {
            try {
                var dropdown = document.getElementById(fieldId + '_dropdown');
                if (!dropdown) {
                    return;
                }

                var currentDisplay = dropdown.style.display;
                if (currentDisplay === 'none' || currentDisplay === '') {
                    dropdown.style.display = 'block';
                } else {
                    dropdown.style.display = 'none';
                }
            } catch (e) {
                // 静默处理错误
            }
        }

        function selectOption(fieldId, value) {
            document.getElementById(fieldId).value = value;
            document.getElementById(fieldId + '_dropdown').style.display = 'none';
        }

        // 点击其他地方关闭下拉框
        function setupClickOutside() {
            if (document.addEventListener) {
                document.addEventListener('click', handleClickOutside);
            } else if (document.attachEvent) {
                document.attachEvent('onclick', handleClickOutside);
            }
        }

        function handleClickOutside(event) {
            event = event || window.event;
            var target = event.target || event.srcElement;
            var dropdowns = document.getElementsByClassName('dropdown-list');
            for (var i = 0; i < dropdowns.length; i++) {
                var dropdown = dropdowns[i];
                var parent = dropdown.parentNode;
                var isInside = false;
                if (parent.contains) {
                    isInside = parent.contains(target);
                } else {
                    var node = target;
                    while (node) {
                        if (node === parent) {
                            isInside = true;
                            break;
                        }
                        node = node.parentNode;
                    }
                }
                if (!isInside) {
                    dropdown.style.display = 'none';
                }
            }
        }

        // 页面加载完成后初始化
        if (document.addEventListener) {
            document.addEventListener('DOMContentLoaded', initPage);
        } else if (document.attachEvent) {
            document.attachEvent('onreadystatechange', function() {
                if (document.readyState === 'complete') {
                    initPage();
                }
            });
        }

        // 兼容旧的window.onload
        window.onload = function() {
            if (!document.getElementById('skillsContainer').innerHTML) {
                initPage();
            }
            generateConfig();
        };
    </script>
</body>
</html>
