s = """

(myj) C:\Project\myj>python myj.py
开始测试...
获取代理配置
获取代理配置成功
服务器x12不在维护,可登录
登陆成功
角色序号[0]角色名[Memory的饲养员]角色id[167572]
进入角色完成
初始化属性
保持在线
房间 卡斯特平原12

=== 新的一天开始 2025-08-03 09:48:32 ===
=== 开始执行日常任务 ===
执行日常任务: 拖把 (优先级: 1)
从map.kasitepingyuan|卡斯特平原12到map.mopcity|驿站
gto 卡斯特平原34|gto 卡斯特平原13|gto 东门|gto 驿站
房间 卡斯特平原12
房间 卡斯特平原34
房间 卡斯特平原13
房间 东门
地图 map.mopcity
房间 驿站
从map.mopcity|驿站到map.mopcity|驿站

房间 驿站
Message左下框:无此道具!
RM左下框:无此道具!
Message左下框:无此道具!
RM左下框:无此道具!
Message左下框:无此道具!
RM左下框:无此道具!
Message左下框:无此道具!
RM左下框:无此道具!
Message左下框:无此道具!
RM左下框:无此道具!
Message左下框:你的等级不足40级，无法历练！
RM左下框:你的等级不足40级，无法历练！
Message左下框:你的等级不足40级，无法历练！
RM左下框:你的等级不足40级，无法历练！
Message左下框:你的等级不足40级，无法历练！
RM左下框:你的等级不足40级，无法历练！
CM右上框: 6191 从少女蜂尸体上获得: 初级升级石x1[09:48]
Message左下框:你的等级不足40级，无法历练！
RM左下框:你的等级不足40级，无法历练！
Message左下框:你的等级不足40级，无法历练！
RM左下框:你的等级不足40级，无法历练！
Message左下框:你的等级不足40级，无法历练！
RM左下框:你的等级不足40级，无法历练！
Message左下框:无法走到那里!
RM左下框:无法走到那里!
房间 痒痒阁
地图 map.maoyougongzuoshi
Npc对话框: 草泥羊: 江湖是险恶的，没事别乱跑吧！
房间 呼噜房
Npc对话框: 金刚呼噜娃: 江湖是险恶的，没事别乱跑吧！
房间 女王宫殿
Npc对话框: 雪之女王: 江湖是险恶的，没事别乱跑吧！
房间 仙子树
Npc对话框: 菊花仙子: 江湖是险恶的，没事别乱跑吧！
房间 胸风轩
Npc对话框: 胸毛哥: 江湖是险恶的，没事别乱跑吧！
房间 擎天宫
Npc对话框: 擎天柱: 江湖是险恶的，没事别乱跑吧！
房间 晶亮殿
Npc对话框: 晶晶靓: 江湖是险恶的，没事别乱跑吧！
房间 痒痒阁
Npc对话框: 草泥羊: 江湖是险恶的，没事别乱跑吧！
房间 果果堂
Npc对话框: 乒乓果冻: 江湖是险恶的，没事别乱跑吧！
房间 黄蜂宫
Npc对话框: 大黄蜂: 江湖是险恶的，没事别乱跑吧！
房间 呼噜房
Npc对话框: 金刚呼噜娃: 江湖是险恶的，没事别乱跑吧！
房间 女王宫殿
Npc对话框: 雪之女王: 江湖是险恶的，没事别乱跑吧！
房间 胸风轩
Npc对话框: 胸毛哥: 江湖是险恶的，没事别乱跑吧！
房间 痒痒阁
房间 驿站
地图 map.mopcity
房间 南门
房间 卡斯特平原16
地图 map.kasitepingyuan
房间 卡斯特平原18
Npc对话框: 许愿树: 每个人每天只能领取一次许愿绳哦~
房间 卡斯特平原16
房间 南门
地图 map.mopcity
房间 比武场
Npc对话框: 黄阿马: 江湖是险恶的，没事别乱跑吧！
房间 添香楼
Npc对话框: 太上老君: 哎~~？ 你不是刚刚来领过了吗？你以为我老人家没记性阿？
房间 十字街口
任务列表type <class 'list'>
任务列表 [['猫隐村/拖把城', '[10] 钱大伯的指引', 'pettask.maoyin.QianDaBoDeZhiYin', True, '#9F9F9F', ''], ['道具店', '[13] 皮货订单', 'pettask.mopcity.PiHuoDingDan', True, '#9F9F9F', ''], ['雪原城', '[18] 血祭奇美拉 (完成:1/1)', 'pettask.xueyuancheng.XueJiQiMeiLa', False, '#9BC813', ''], ['猫隐村', '[4] 装备店老板的邀请', 'pettask.maoyin.ZhuangBeiDianLaoBanDeYaoQing', True, '#9F9F9F', '']]
Npc对话框: 帝国使者: 今天你的任务是交10个虎眼石给我。
Npc对话框: 帝国使者: 你身上的宝石数量还不够~！
房间 银月大厅
地图 map.yinyue
房间 银月广场
RM左下框:Memory的饲养员战战兢兢的摸了一下传送石，脚下突然出现了一个黑洞，Memory的饲养员还来不急反应，已经被一片黑暗包围了!
房间 十字街口
RM左下框:一阵惊恐的叫声由远而近，只见Memory的饲养员紧闭双眼，脸色发白，从空中悠悠飘下。脚都落了地，还一副不知道发生了什么事的样子。
地图 map.mopcity
房间 驿站
从map.mopcity|驿站到map.kasitepingyuan|卡斯特平原12
gto 东门|gto 卡斯特平原13|gto 卡斯特平原34|gto 卡斯特平原12
房间 驿站
房间 东门
房间 卡斯特平原13
地图 map.kasitepingyuan
房间 卡斯特平原34
房间 卡斯特平原12
从map.kasitepingyuan|卡斯特平原12到map.kasitepingyuan|卡斯特平原12

房间 卡斯特平原12
日常任务 拖把 完成
=== 日常任务全部完成 ===
开始执行闲时任务: 刷怪 (闲时_刷怪)
正在装备技能:龙葵匕刃
刷怪换装包中无目标刺客之刃,默认已装备
从map.kasitepingyuan|卡斯特平原12到map.maoyin|装备店
gto 卡斯特平原34|gto 卡斯特平原13|gto 东门|gto 驿站|flyto 猫隐村|gto 装备店
房间 卡斯特平原12
房间 卡斯特平原34
房间 卡斯特平原13
房间 东门
地图 map.mopcity
房间 驿站
房间 猫隐村驿站
地图 map.maoyin
房间 装备店
从map.maoyin|装备店到map.maoyin|装备店

房间 装备店
RM左下框:没有任何需要修理的装备!
房间 教堂
Npc对话框: 修女: Memory的饲养员气血充沛,不需要治疗啊!
从map.maoyin|教堂到map.kasitepingyuan|卡斯特平原34
gto 猫隐村驿站|flyto 拖把城|gto 东门|gto 卡斯特平原13|gto 卡斯特平原34
房间 教堂
房间 猫隐村驿站
房间 驿站
地图 map.mopcity
房间 东门
房间 卡斯特平原13
地图 map.kasitepingyuan
房间 卡斯特平原34
从map.kasitepingyuan|卡斯特平原34到map.kasitepingyuan|卡斯特平原34

房间 卡斯特平原34
房间 卡斯特平原12
进入战斗
Message左下框:你向豆芽君发起攻击!
RM左下框:你向豆芽君发起攻击!
RM左下框:你向豆芽君施放出伏击[一级],造成311点伤害！豆芽君中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:豆芽君被你杀死了...
RM左下框:豆芽君被你杀死了...
Message左下框:豆芽君(总斩数:20348 总经验:0)被杀死了!
RM左下框:豆芽君(总斩数:20348 总经验:0)被杀死了!
房间 卡斯特平原12
Message左下框:无法施展!
RM左下框:无法施展!
进入战斗
Message左下框:你向蚱蜢发起攻击!
RM左下框:你向蚱蜢发起攻击!
RM左下框:你向蚱蜢施放出伏击[一级],造成326点伤害！蚱蜢中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:蚱蜢被你杀死了...
RM左下框:蚱蜢被你杀死了...
Message左下框:蚱蜢(总斩数:1676 总经验:0)被杀死了!
RM左下框:蚱蜢(总斩数:1676 总经验:0)被杀死了!
房间 卡斯特平原12
进入战斗
Message左下框:你向豆芽君发起攻击!
RM左下框:你向豆芽君发起攻击!
RM左下框:你向豆芽君施放出伏击[一级],造成335点伤害！豆芽君中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:豆芽君被你杀死了...
RM左下框:豆芽君被你杀死了...
Message左下框:豆芽君(总斩数:20349 总经验:0)被杀死了!
RM左下框:豆芽君(总斩数:20349 总经验:0)被杀死了!
房间 卡斯特平原12
房间 卡斯特平原15
进入战斗
Message左下框:你向巨钳螃蟹发起攻击!
RM左下框:你向巨钳螃蟹发起攻击!
RM左下框:你向巨钳螃蟹施放出伏击[一级],造成345点伤害！巨钳螃蟹中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
战斗-对手状态:沉默
Message左下框:使用凿击! 15秒 后可再次使用!
RM左下框:使用凿击! 15秒 后可再次使用!
战斗-对手状态:沉默 昏迷
RM左下框:你向巨钳螃蟹施放出凿击[1级]！造成 290 点伤害!
战斗相关,暂不处理
战斗相关,暂不处理
战斗相关,暂不处理
未处理格式
{'function_name': 'showLeftTalk', 'parameters': [{'type': 'string', 'value': '凿击[1级]!', 'raw': "'凿击[1级]!'"}], 'raw_call': "p.showLeftTalk('凿击[1级]!')", 'end_pos': 431}
Message左下框:巨钳螃蟹被你杀死了...
RM左下框:巨钳螃蟹被你杀死了...
Message左下框:巨钳螃蟹(总斩数:13621 总经验:37831)被杀死了!
RM左下框:巨钳螃蟹(总斩数:13621 总经验:37831)被杀死了!
房间 卡斯特平原15
房间 卡斯特平原15
{'function_name': 'setFightTaskImg', 'parameters': [{'type': 'string', 'value': 'null', 'raw': "'null'"}], 'raw_call': "p.setFightTaskImg('null')", 'end_pos': 25}
Message左下框:Memory的饲养员正忙着呢。无法使出伏击[一级]!
RM左下框:Memory的饲养员正忙着呢。无法使出伏击[一级]!
Message左下框:你的宠物正忙着呢!
RM左下框:你的宠物正忙着呢!
进入战斗
Message左下框:你向蚱蜢发起攻击!
RM左下框:你向蚱蜢发起攻击!
RM左下框:你向蚱蜢施放出伏击[一级],造成328点伤害！蚱蜢中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:蚱蜢被你杀死了...
RM左下框:蚱蜢被你杀死了...
Message左下框:蚱蜢(总斩数:1677 总经验:0)被杀死了!
RM左下框:蚱蜢(总斩数:1677 总经验:0)被杀死了!
房间 卡斯特平原15
进入战斗
Message左下框:你向巨钳螃蟹发起攻击!
RM左下框:你向巨钳螃蟹发起攻击!
RM左下框:你向巨钳螃蟹施放出伏击[一级],造成342点伤害！巨钳螃蟹中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:巨钳螃蟹被你杀死了...
RM左下框:巨钳螃蟹被你杀死了...
Message左下框:巨钳螃蟹(总斩数:13622 总经验:37831)被杀死了!
RM左下框:巨钳螃蟹(总斩数:13622 总经验:37831)被杀死了!
房间 卡斯特平原15
进入战斗
Message左下框:你向蚱蜢发起攻击!
RM左下框:你向蚱蜢发起攻击!
RM左下框:你向蚱蜢施放出伏击[一级],造成333点伤害！蚱蜢中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:蚱蜢被你杀死了...
RM左下框:蚱蜢被你杀死了...
Message左下框:蚱蜢(总斩数:1678 总经验:0)被杀死了!
RM左下框:蚱蜢(总斩数:1678 总经验:0)被杀死了!
房间 卡斯特平原15
进入战斗
Message左下框:你向豆芽君发起攻击!
RM左下框:你向豆芽君发起攻击!
RM左下框:你向豆芽君施放出伏击[一级],造成327点伤害！豆芽君中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:豆芽君被你杀死了...
RM左下框:豆芽君被你杀死了...
Message左下框:豆芽君(总斩数:20350 总经验:0)被杀死了!
RM左下框:豆芽君(总斩数:20350 总经验:0)被杀死了!
房间 卡斯特平原15
进入战斗
Message左下框:你向豆芽君发起攻击!
RM左下框:你向豆芽君发起攻击!
RM左下框:你向豆芽君施放出伏击[一级],造成353点伤害！豆芽君中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:豆芽君被你杀死了...
RM左下框:豆芽君被你杀死了...
Message左下框:豆芽君(总斩数:20351 总经验:0)被杀死了!
RM左下框:豆芽君(总斩数:20351 总经验:0)被杀死了!
房间 卡斯特平原15
Message左下框:无法施展!
RM左下框:无法施展!
进入战斗
Message左下框:你向蚱蜢发起攻击!
RM左下框:你向蚱蜢发起攻击!
RM左下框:你向蚱蜢施放出伏击[一级],造成342点伤害！蚱蜢中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:蚱蜢被你杀死了...
RM左下框:蚱蜢被你杀死了...
Message左下框:蚱蜢(总斩数:1679 总经验:0)被杀死了!
RM左下框:蚱蜢(总斩数:1679 总经验:0)被杀死了!
房间 卡斯特平原15
Message左下框:无法施展!
RM左下框:无法施展!
房间 卡斯特平原41
进入战斗
Message左下框:你向蚱蜢发起攻击!
RM左下框:你向蚱蜢发起攻击!
RM左下框:你向蚱蜢施放出伏击[一级],造成330点伤害！蚱蜢中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:蚱蜢被你杀死了...
RM左下框:蚱蜢被你杀死了...
Message左下框:蚱蜢(总斩数:1680 总经验:0)被杀死了!
RM左下框:蚱蜢(总斩数:1680 总经验:0)被杀死了!
房间 卡斯特平原41
进入战斗
Message左下框:你向豆芽君发起攻击!
RM左下框:你向豆芽君发起攻击!
RM左下框:你向豆芽君施放出伏击[一级],造成342点伤害！豆芽君中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:豆芽君被你杀死了...
RM左下框:豆芽君被你杀死了...
Message左下框:豆芽君(总斩数:20352 总经验:0)被杀死了!
RM左下框:豆芽君(总斩数:20352 总经验:0)被杀死了!
房间 卡斯特平原41
进入战斗
Message左下框:你向豆芽君发起攻击!
RM左下框:你向豆芽君发起攻击!
RM左下框:你向豆芽君施放出伏击[一级],造成332点伤害！豆芽君中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:豆芽君被你杀死了...
RM左下框:豆芽君被你杀死了...
Message左下框:豆芽君(总斩数:20353 总经验:0)被杀死了!
RM左下框:豆芽君(总斩数:20353 总经验:0)被杀死了!
房间 卡斯特平原41
进入战斗
Message左下框:你向蚱蜢发起攻击!
RM左下框:你向蚱蜢发起攻击!
RM左下框:你向蚱蜢施放出伏击[一级],造成307点伤害！蚱蜢中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:蚱蜢被你杀死了...
RM左下框:蚱蜢被你杀死了...
Message左下框:蚱蜢(总斩数:1681 总经验:0)被杀死了!
RM左下框:蚱蜢(总斩数:1681 总经验:0)被杀死了!
房间 卡斯特平原41
进入战斗
Message左下框:你向豆芽君发起攻击!
RM左下框:你向豆芽君发起攻击!
RM左下框:你向豆芽君施放出伏击[一级],造成340点伤害！豆芽君中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:豆芽君被你杀死了...
RM左下框:豆芽君被你杀死了...
Message左下框:豆芽君(总斩数:20354 总经验:0)被杀死了!
RM左下框:豆芽君(总斩数:20354 总经验:0)被杀死了!
房间 卡斯特平原41
Message左下框:无法施展!
RM左下框:无法施展!
进入战斗
Message左下框:你向蚱蜢发起攻击!
RM左下框:你向蚱蜢发起攻击!
RM左下框:你向蚱蜢施放出伏击[一级],造成318点伤害！蚱蜢中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:蚱蜢被你杀死了...
RM左下框:蚱蜢被你杀死了...
Message左下框:蚱蜢(总斩数:1682 总经验:0)被杀死了!
RM左下框:蚱蜢(总斩数:1682 总经验:0)被杀死了!
房间 卡斯特平原41
战斗-对手状态:沉默
房间 卡斯特平原41
{'function_name': 'setFightTaskImg', 'parameters': [{'type': 'string', 'value': 'null', 'raw': "'null'"}], 'raw_call': "p.setFightTaskImg('null')", 'end_pos': 25}
Message左下框:Memory的饲养员正忙着呢。无法使出伏击[一级]!
RM左下框:Memory的饲养员正忙着呢。无法使出伏击[一级]!
Message左下框:你的宠物正忙着呢!
RM左下框:你的宠物正忙着呢!
Message左下框:Memory的饲养员正忙着呢。无法使出伏击[一级]!
RM左下框:Memory的饲养员正忙着呢。无法使出伏击[一级]!
Message左下框:你的宠物正忙着呢!
RM左下框:你的宠物正忙着呢!
Message左下框:Memory的饲养员正忙着呢。无法使出伏击[一级]!
RM左下框:Memory的饲养员正忙着呢。无法使出伏击[一级]!
Message左下框:你的宠物正忙着呢!
RM左下框:你的宠物正忙着呢!
Message左下框:Memory的饲养员正忙着呢。无法使出伏击[一级]!
RM左下框:Memory的饲养员正忙着呢。无法使出伏击[一级]!
Message左下框:你的宠物正忙着呢!
RM左下框:你的宠物正忙着呢!
进入战斗
Message左下框:你向蚱蜢发起攻击!
RM左下框:你向蚱蜢发起攻击!
RM左下框:你向蚱蜢施放出伏击[一级],造成339点伤害！蚱蜢中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:蚱蜢被你杀死了...
RM左下框:蚱蜢被你杀死了...
Message左下框:蚱蜢(总斩数:1683 总经验:0)被杀死了!
RM左下框:蚱蜢(总斩数:1683 总经验:0)被杀死了!
房间 卡斯特平原41
进入战斗
Message左下框:你向豆芽君发起攻击!
RM左下框:你向豆芽君发起攻击!
RM左下框:你向豆芽君施放出伏击[一级],造成347点伤害！豆芽君中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:豆芽君被你杀死了...
RM左下框:豆芽君被你杀死了...
Message左下框:豆芽君(总斩数:20355 总经验:0)被杀死了!
RM左下框:豆芽君(总斩数:20355 总经验:0)被杀死了!
RM左下框:获得 神符石 x 1 !
房间 卡斯特平原41
进入战斗
Message左下框:你向豆芽君发起攻击!
RM左下框:你向豆芽君发起攻击!
RM左下框:你向豆芽君施放出伏击[一级],造成328点伤害！豆芽君中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:豆芽君被你杀死了...
RM左下框:豆芽君被你杀死了...
Message左下框:豆芽君(总斩数:20356 总经验:0)被杀死了!
RM左下框:豆芽君(总斩数:20356 总经验:0)被杀死了!
房间 卡斯特平原41
进入战斗
Message左下框:你向豆芽君发起攻击!
RM左下框:你向豆芽君发起攻击!
RM左下框:你向豆芽君施放出伏击[一级],造成351点伤害！豆芽君中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:豆芽君被你杀死了...
RM左下框:豆芽君被你杀死了...
Message左下框:豆芽君(总斩数:20357 总经验:0)被杀死了!
RM左下框:豆芽君(总斩数:20357 总经验:0)被杀死了!
房间 卡斯特平原41
Message左下框:无法施展!
RM左下框:无法施展!
房间 卡斯特平原19
进入战斗
Message左下框:你向蚱蜢发起攻击!
RM左下框:你向蚱蜢发起攻击!
RM左下框:你向蚱蜢施放出伏击[一级],造成306点伤害！蚱蜢中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:蚱蜢被你杀死了...
RM左下框:蚱蜢被你杀死了...
Message左下框:蚱蜢(总斩数:1684 总经验:0)被杀死了!
RM左下框:蚱蜢(总斩数:1684 总经验:0)被杀死了!
房间 卡斯特平原19
进入战斗
Message左下框:你向蚱蜢发起攻击!
RM左下框:你向蚱蜢发起攻击!
RM左下框:你向蚱蜢施放出伏击[一级],造成312点伤害！蚱蜢中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:蚱蜢被你杀死了...
RM左下框:蚱蜢被你杀死了...
Message左下框:蚱蜢(总斩数:1685 总经验:0)被杀死了!
RM左下框:蚱蜢(总斩数:1685 总经验:0)被杀死了!
房间 卡斯特平原19
进入战斗
Message左下框:你向巨钳螃蟹发起攻击!
RM左下框:你向巨钳螃蟹发起攻击!
RM左下框:你向巨钳螃蟹施放出伏击[一级],造成328点伤害！巨钳螃蟹中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
战斗-对手状态:沉默
Message左下框:使用凿击! 15秒 后可再次使用!
RM左下框:使用凿击! 15秒 后可再次使用!
战斗-对手状态:沉默 昏迷
RM左下框:你向巨钳螃蟹施放出凿击[1级]！造成 314 点伤害!
战斗相关,暂不处理
战斗相关,暂不处理
战斗相关,暂不处理
未处理格式
{'function_name': 'showLeftTalk', 'parameters': [{'type': 'string', 'value': '凿击[1级]!', 'raw': "'凿击[1级]!'"}], 'raw_call': "p.showLeftTalk('凿击[1级]!')", 'end_pos': 431}
Message左下框:巨钳螃蟹被你杀死了...
RM左下框:巨钳螃蟹被你杀死了...
Message左下框:巨钳螃蟹(总斩数:13623 总经验:37831)被杀死了!
RM左下框:巨钳螃蟹(总斩数:13623 总经验:37831)被杀死了!
RM左下框:获得 奥术弹1级技能果实 x 1 !
CM右上框: りゅう霸天の橘乄 从巨蜂尸体上获得: 初级升级石x1[09:49]
房间 卡斯特平原19
房间 卡斯特平原19
{'function_name': 'setFightTaskImg', 'parameters': [{'type': 'string', 'value': 'null', 'raw': "'null'"}], 'raw_call': "p.setFightTaskImg('null')", 'end_pos': 25}
Message左下框:Memory的饲养员正忙着呢。无法使出伏击[一级]!
RM左下框:Memory的饲养员正忙着呢。无法使出伏击[一级]!
Message左下框:你的宠物正忙着呢!
RM左下框:你的宠物正忙着呢!
进入战斗
Message左下框:你向蚱蜢发起攻击!
RM左下框:你向蚱蜢发起攻击!
RM左下框:你向蚱蜢施放出伏击[一级],造成357点伤害！蚱蜢中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:龙葵匕刃的熟练度提高 1 点!
RM左下框:龙葵匕刃的熟练度提高 1 点!
Message左下框:蚱蜢被你杀死了...
RM左下框:蚱蜢被你杀死了...
Message左下框:蚱蜢(总斩数:1686 总经验:0)被杀死了!
RM左下框:蚱蜢(总斩数:1686 总经验:0)被杀死了!
房间 卡斯特平原19
战斗-对手状态:沉默
房间 卡斯特平原19
{'function_name': 'setFightTaskImg', 'parameters': [{'type': 'string', 'value': 'null', 'raw': "'null'"}], 'raw_call': "p.setFightTaskImg('null')", 'end_pos': 25}
Message左下框:Memory的饲养员正忙着呢。无法使出伏击[一级]!
RM左下框:Memory的饲养员正忙着呢。无法使出伏击[一级]!
Message左下框:你的宠物正忙着呢!
RM左下框:你的宠物正忙着呢!
Message左下框:Memory的饲养员正忙着呢。无法使出伏击[一级]!
RM左下框:Memory的饲养员正忙着呢。无法使出伏击[一级]!
Message左下框:你的宠物正忙着呢!
RM左下框:你的宠物正忙着呢!
Message左下框:Memory的饲养员正忙着呢。无法使出伏击[一级]!
RM左下框:Memory的饲养员正忙着呢。无法使出伏击[一级]!
Message左下框:你的宠物正忙着呢!
RM左下框:你的宠物正忙着呢!
Message左下框:Memory的饲养员正忙着呢。无法使出伏击[一级]!
RM左下框:Memory的饲养员正忙着呢。无法使出伏击[一级]!
Message左下框:你的宠物正忙着呢!
RM左下框:你的宠物正忙着呢!
进入战斗
Message左下框:你向巨钳螃蟹发起攻击!
RM左下框:你向巨钳螃蟹发起攻击!
RM左下框:你向巨钳螃蟹施放出伏击[一级],造成361点伤害！巨钳螃蟹中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:巨钳螃蟹被你杀死了...
RM左下框:巨钳螃蟹被你杀死了...
Message左下框:巨钳螃蟹(总斩数:13624 总经验:37831)被杀死了!
RM左下框:巨钳螃蟹(总斩数:13624 总经验:37831)被杀死了!
房间 卡斯特平原19
进入战斗
Message左下框:你向巨钳螃蟹发起攻击!
RM左下框:你向巨钳螃蟹发起攻击!
RM左下框:你向巨钳螃蟹施放出伏击[一级],造成331点伤害！巨钳螃蟹中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:命令已收到!
RM左下框:命令已收到!
{'function_name': 'setFightTaskImg', 'parameters': [{'type': 'string', 'value': 'img/bar/skills/shizizhan.gif', 'raw': "'img/bar/skills/shizizhan.gif'"}], 'raw_call': "p.setFightTaskImg('img/bar/skills/shizizhan.gif')", 'end_pos': 49}
Message左下框:使用强力打击! 6秒 后可再次使用!
RM左下框:使用强力打击! 6秒 后可再次使用!
RM左下框:你向巨钳螃蟹施放出强力打击[1级]！造成 285 点伤害!
战斗相关,暂不处理
战斗相关,暂不处理
战斗相关,暂不处理
未处理格式
{'function_name': 'showLeftTalk', 'parameters': [{'type': 'string', 'value': '强力打击[1级]!', 'raw': "'强力打击[1级]!'"}], 'raw_call': "p.showLeftTalk('强力打击[1级]!')", 'end_pos': 437}
Message左下框:巨钳螃蟹被你杀死了...
RM左下框:巨钳螃蟹被你杀死了...
Message左下框:巨钳螃蟹(总斩数:13625 总经验:37831)被杀死了!
RM左下框:巨钳螃蟹(总斩数:13625 总经验:37831)被杀死了!
房间 卡斯特平原19
{'function_name': 'setFightTaskImg', 'parameters': [{'type': 'string', 'value': 'null', 'raw': "'null'"}], 'raw_call': "p.setFightTaskImg('null')", 'end_pos': 25}
{'function_name': 'setFightTaskImg', 'parameters': [{'type': 'string', 'value': 'null', 'raw': "'null'"}], 'raw_call': "p.setFightTaskImg('null')", 'end_pos': 25}
进入战斗
Message左下框:你向豆芽君发起攻击!
RM左下框:你向豆芽君发起攻击!
RM左下框:你向豆芽君施放出伏击[一级],造成307点伤害！豆芽君中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:豆芽君被你杀死了...
RM左下框:豆芽君被你杀死了...
Message左下框:豆芽君(总斩数:20358 总经验:0)被杀死了!
RM左下框:豆芽君(总斩数:20358 总经验:0)被杀死了!
RM左下框:获得 稻草 x 1 !
房间 卡斯特平原19
房间 卡斯特平原42
进入战斗
Message左下框:你向蚱蜢发起攻击!
RM左下框:你向蚱蜢发起攻击!
RM左下框:你向蚱蜢施放出伏击[一级],造成328点伤害！蚱蜢中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:龙葵匕刃的熟练度提高 1 点!
RM左下框:龙葵匕刃的熟练度提高 1 点!
Message左下框:蚱蜢被你杀死了...
RM左下框:蚱蜢被你杀死了...
Message左下框:蚱蜢(总斩数:1687 总经验:0)被杀死了!
RM左下框:蚱蜢(总斩数:1687 总经验:0)被杀死了!
房间 卡斯特平原42
进入战斗
Message左下框:你向豆芽君发起攻击!
RM左下框:你向豆芽君发起攻击!
RM左下框:你向豆芽君施放出伏击[一级],造成354点伤害！豆芽君中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:豆芽君被你杀死了...
RM左下框:豆芽君被你杀死了...
Message左下框:豆芽君(总斩数:20359 总经验:0)被杀死了!
RM左下框:豆芽君(总斩数:20359 总经验:0)被杀死了!
房间 卡斯特平原42
房间 卡斯特平原21
进入战斗
Message左下框:你向巨钳螃蟹发起攻击!
RM左下框:你向巨钳螃蟹发起攻击!
RM左下框:你向巨钳螃蟹施放出伏击[一级],造成347点伤害！巨钳螃蟹中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:巨钳螃蟹被你杀死了...
RM左下框:巨钳螃蟹被你杀死了...
Message左下框:巨钳螃蟹(总斩数:13626 总经验:37831)被杀死了!
RM左下框:巨钳螃蟹(总斩数:13626 总经验:37831)被杀死了!
房间 卡斯特平原21
进入战斗
Message左下框:你向巨钳螃蟹发起攻击!
RM左下框:你向巨钳螃蟹发起攻击!
RM左下框:你向巨钳螃蟹施放出伏击[一级],造成317点伤害！巨钳螃蟹中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
战斗-对手状态:沉默
RM左下框:尘陌从西门走了过来
战斗相关,暂不处理
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:你对巨钳螃蟹造成313 点伤害!
RM左下框:你对巨钳螃蟹造成313 点伤害!
Message左下框:巨钳螃蟹被你杀死了...
RM左下框:巨钳螃蟹被你杀死了...
Message左下框:巨钳螃蟹(总斩数:13627 总经验:37831)被杀死了!
RM左下框:巨钳螃蟹(总斩数:13627 总经验:37831)被杀死了!
RM左下框:获得 螃蟹外壳 x 1 !
房间 卡斯特平原21
{'function_name': 'setFightTaskImg', 'parameters': [{'type': 'string', 'value': 'null', 'raw': "'null'"}], 'raw_call': "p.setFightTaskImg('null')", 'end_pos': 25}
RM左下框:仙狐奇缘从卡斯特平原42走了过来
进入战斗
Message左下框:你向巨钳螃蟹发起攻击!
RM左下框:你向巨钳螃蟹发起攻击!
RM左下框:你向巨钳螃蟹施放出伏击[一级],造成358点伤害！巨钳螃蟹中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
战斗-对手状态:沉默
RM左下框:仙狐奇缘向卡斯特平原17离开
Message左下框:命令已收到!
RM左下框:命令已收到!
{'function_name': 'setFightTaskImg', 'parameters': [{'type': 'string', 'value': 'img/bar/skills/shizizhan.gif', 'raw': "'img/bar/skills/shizizhan.gif'"}], 'raw_call': "p.setFightTaskImg('img/bar/skills/shizizhan.gif')", 'end_pos': 49}
RM左下框:尘陌向卡斯特平原22离开
CM右上框: りゅう霸天の橘乄 从稻草人尸体上获得: 初级升级石x1[09:49]
Message左下框:使用强力打击! 6秒 后可再次使用!
RM左下框:使用强力打击! 6秒 后可再次使用!
RM左下框:你向巨钳螃蟹施放出强力打击[1级]！造成 283 点伤害!
战斗相关,暂不处理
战斗相关,暂不处理
战斗相关,暂不处理
未处理格式
{'function_name': 'showLeftTalk', 'parameters': [{'type': 'string', 'value': '强力打击[1级]!', 'raw': "'强力打击[1级]!'"}], 'raw_call': "p.showLeftTalk('强力打击[1级]!')", 'end_pos': 438}
Message左下框:巨钳螃蟹被你杀死了...
RM左下框:巨钳螃蟹被你杀死了...
Message左下框:巨钳螃蟹(总斩数:13628 总经验:37831)被杀死了!
RM左下框:巨钳螃蟹(总斩数:13628 总经验:37831)被杀死了!
房间 卡斯特平原21
{'function_name': 'setFightTaskImg', 'parameters': [{'type': 'string', 'value': 'null', 'raw': "'null'"}], 'raw_call': "p.setFightTaskImg('null')", 'end_pos': 25}
{'function_name': 'setFightTaskImg', 'parameters': [{'type': 'string', 'value': 'null', 'raw': "'null'"}], 'raw_call': "p.setFightTaskImg('null')", 'end_pos': 25}
进入战斗
Message左下框:你向巨钳螃蟹发起攻击!
RM左下框:你向巨钳螃蟹发起攻击!
RM左下框:你向巨钳螃蟹施放出伏击[一级],造成299点伤害！巨钳螃蟹中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:龙葵匕刃的熟练度提高 1 点!
RM左下框:龙葵匕刃的熟练度提高 1 点!
战斗-对手状态:沉默
CM右上框: りゅう贡の橘乄 从兵蚁L尸体上获得: 初级升级石x1[09:49]
Message左下框:使用猛抽! 30秒 后可再次使用!
RM左下框:使用猛抽! 30秒 后可再次使用!
战斗-对手状态:沉默 昏迷
RM左下框:你向巨钳螃蟹施放出猛抽！巨钳螃蟹被昏迷了!
战斗相关,暂不处理
战斗相关,暂不处理
战斗相关,暂不处理
未处理格式
{'function_name': 'showLeftTalk', 'parameters': [{'type': 'string', 'value': '猛抽!', 'raw': "'猛抽!'"}], 'raw_call': "p.showLeftTalk('猛抽!')", 'end_pos': 422}
战斗相关,暂不处理
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:你对巨钳螃蟹造成284 点伤害!
RM左下框:你对巨钳螃蟹造成284 点伤害!
Message左下框:巨钳螃蟹被你杀死了...
RM左下框:巨钳螃蟹被你杀死了...
Message左下框:巨钳螃蟹(总斩数:13629 总经验:37831)被杀死了!
RM左下框:巨钳螃蟹(总斩数:13629 总经验:37831)被杀死了!
房间 卡斯特平原21
{'function_name': 'setFightTaskImg', 'parameters': [{'type': 'string', 'value': 'null', 'raw': "'null'"}], 'raw_call': "p.setFightTaskImg('null')", 'end_pos': 25}
进入战斗
Message左下框:你向巨钳螃蟹发起攻击!
RM左下框:你向巨钳螃蟹发起攻击!
RM左下框:你向巨钳螃蟹施放出伏击[一级],造成347点伤害！巨钳螃蟹中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
战斗-对手状态:沉默
Message左下框:使用凿击! 15秒 后可再次使用!
RM左下框:使用凿击! 15秒 后可再次使用!
战斗-对手状态:沉默 昏迷
RM左下框:你向巨钳螃蟹施放出凿击[1级]！造成 297 点伤害!
战斗相关,暂不处理
战斗相关,暂不处理
战斗相关,暂不处理
未处理格式
{'function_name': 'showLeftTalk', 'parameters': [{'type': 'string', 'value': '凿击[1级]!', 'raw': "'凿击[1级]!'"}], 'raw_call': "p.showLeftTalk('凿击[1级]!')", 'end_pos': 431}
Message左下框:巨钳螃蟹被你杀死了...
RM左下框:巨钳螃蟹被你杀死了...
Message左下框:巨钳螃蟹(总斩数:13630 总经验:37831)被杀死了!
RM左下框:巨钳螃蟹(总斩数:13630 总经验:37831)被杀死了!
房间 卡斯特平原21
房间 卡斯特平原21
{'function_name': 'setFightTaskImg', 'parameters': [{'type': 'string', 'value': 'null', 'raw': "'null'"}], 'raw_call': "p.setFightTaskImg('null')", 'end_pos': 25}
房间 卡斯特平原17
进入战斗
Message左下框:你向蚱蜢发起攻击!
RM左下框:你向蚱蜢发起攻击!
RM左下框:你向蚱蜢施放出伏击[一级],造成348点伤害！蚱蜢中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:蚱蜢被你杀死了...
RM左下框:蚱蜢被你杀死了...
Message左下框:蚱蜢(总斩数:1688 总经验:0)被杀死了!
RM左下框:蚱蜢(总斩数:1688 总经验:0)被杀死了!
房间 卡斯特平原17
进入战斗
Message左下框:你向巨钳螃蟹发起攻击!
RM左下框:你向巨钳螃蟹发起攻击!
RM左下框:你向巨钳螃蟹施放出伏击[一级],造成351点伤害！巨钳螃蟹中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
战斗-对手状态:沉默
Message左下框:命令已收到!
RM左下框:命令已收到!
{'function_name': 'setFightTaskImg', 'parameters': [{'type': 'string', 'value': 'img/bar/skills/shizizhan.gif', 'raw': "'img/bar/skills/shizizhan.gif'"}], 'raw_call': "p.setFightTaskImg('img/bar/skills/shizizhan.gif')", 'end_pos': 49}
Message左下框:使用强力打击! 6秒 后可再次使用!
RM左下框:使用强力打击! 6秒 后可再次使用!
RM左下框:你向巨钳螃蟹施放出强力打击[1级]！造成 269 点伤害!
战斗相关,暂不处理
战斗相关,暂不处理
战斗相关,暂不处理
未处理格式
{'function_name': 'showLeftTalk', 'parameters': [{'type': 'string', 'value': '强力打击[1级]!', 'raw': "'强力打击[1级]!'"}], 'raw_call': "p.showLeftTalk('强力打击[1级]!')", 'end_pos': 438}
Message左下框:巨钳螃蟹被你杀死了...
RM左下框:巨钳螃蟹被你杀死了...
Message左下框:巨钳螃蟹(总斩数:13631 总经验:37831)被杀死了!
RM左下框:巨钳螃蟹(总斩数:13631 总经验:37831)被杀死了!
房间 卡斯特平原17
{'function_name': 'setFightTaskImg', 'parameters': [{'type': 'string', 'value': 'null', 'raw': "'null'"}], 'raw_call': "p.setFightTaskImg('null')", 'end_pos': 25}
{'function_name': 'setFightTaskImg', 'parameters': [{'type': 'string', 'value': 'null', 'raw': "'null'"}], 'raw_call': "p.setFightTaskImg('null')", 'end_pos': 25}
进入战斗
Message左下框:你向巨钳螃蟹发起攻击!
RM左下框:你向巨钳螃蟹发起攻击!
RM左下框:你向巨钳螃蟹施放出伏击[一级],造成308点伤害！巨钳螃蟹中埋伏了!
战斗相关,暂不处理
战斗相关,暂不处理
Message左下框:龙葵匕刃的熟练度提高 1 点!
RM左下框:龙葵匕刃的熟练度提高 1 点!
战斗-对手状态:沉默
Traceback (most recent call last):
  File "C:\Project\myj\myj.py", line 1136, in <module>
    function_test()
    ~~~~~~~~~~~~~^^
  File "C:\Project\myj\myj.py", line 1129, in function_test
    time.sleep(9999)
    ~~~~~~~~~~^^^^^^
KeyboardInterrupt
^C
(myj) C:\Project\myj>
"""
for i in s.split('\n'):
    if i.startswith("""{'function_name': 'showLeftTalk',"""):
        print(i)
for i in s.split('\n'):
    if i.startswith("""{'function_name': 'setFightTaskImg'"""):
        print(i)