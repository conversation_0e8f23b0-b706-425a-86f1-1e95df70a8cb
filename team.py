from myj import MYJ
import time
from map import ret_way

config = {
    "服务器": "s69",
    "账号": "yfydq1",
    "密码": "yfydq0101",
    "角色序号": "0",    
}

class MYJTeam:
    def __init__(self, configs):
        self.myj_objects = [MYJ(config) for config in configs]
    
    def 单角色执行命令(self,s,index=0):
        self.myj_objects[index].send_orders(s)

    def 全体执行命令(self,s):
        局_命令列表 = s.split('|')
        for 局_单条命令 in 局_命令列表:
            for myj in self.myj_objects:
                myj.send_orders(局_单条命令,0)
            time.sleep(self.myj_objects[0].延迟)

    def gto_异地(self, target):
        for myj in self.myj_objects:
            myj.gto(target)

    def gto_同地(self,target):
        局_跑图命令 = ret_way(self.myj_objects[0].地图 + '|' + self.myj_objects[0].房间, target)
        self.全体执行命令(局_跑图命令)
        self.gto_异地(target)

    def 组队(self):
        for index in range(len(self.myj_objects)):
            if index == 0:
                pass
            else:
                self.myj_objects[0].send_orders(f'foo rank add {self.myj_objects[index].宠物名}',0)
                self.myj_objects[index].send_orders(f'foo rank agree {self.myj_objects[0].petId}|follow none|follow {self.myj_objects[0].宠物名}')

    def 悬赏(self):
        while True:
            # 外层无限循环
            # 走到拖把皇宫大门
            self.gto_异地('map.mopcity|皇宫大门')
            break
    
if __name__ == '__main__':
    configs = [
        {
            "服务器": "s69",
            "账号": "yfydq1",
            "密码": "yfydq0101",
            "角色序号": "0",    
        },
        {
            "服务器": "s69",
            "账号": "yfydq2",
            "密码": "yfydq0101",
            "角色序号": "0",    
        },
        {
            "服务器": "s69",
            "账号": "yfydq3",
            "密码": "yfydq0101",
            "角色序号": "0",    
        }
    ]
    team = MYJTeam(configs)
    team.gto_异地('map.mopcity|皇宫大门')
    team.组队()
    team.单角色执行命令('gto 驿站')
    team.全体执行命令('gto 皇宫')
    team.单角色执行命令('gto 皇宫大门|gettask one')
    team.gto_同地('map.daxueshan|西坡_05')
    print('这里是打怪')
    team.全体执行命令(ret_way(f'{team.myj_objects[0].地图}|{team.myj_objects[0].房间}', 'map.mopcity|皇宫大门'))
    input()