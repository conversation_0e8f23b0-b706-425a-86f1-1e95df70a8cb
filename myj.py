# 第一部分 库
from urllib.parse import quote  # 登录时,把中文账号转码使用
import requests                 # 请求通用,网络
import time                     # 全局通用,时间
import re                       # 全局通用,正则
import keyboard                 # 全局通用,键盘
import json                     # 全局通用,json
import datetime                 # 全局通用,日期时间
from threading import Lock      # 线程锁,用在命令
# 第二部分 自写代码
from mythread import MyThread, ThreadState   # 系统级线程,全局使用
from default import default_speed, default_reconnect, daily_tb_orders, daily_mh_orders   # 内置默认频率,单位秒
from proxy import get_proxy         # 获取代理
from yzm import get_yzm             # 获取验证码
from js_parser import parse_js_code, ret_packages, js_to_python # 解析js代码
from map import ret_way, MyjRoute
from auth_time import get_time_str


class MYJ:
    def __init__(self, config):
        # 内置默认配置
        self.延迟 = default_speed  # 内置默认频率,单位秒
        self.服务器 = config['服务器']
        self.账号 = config['账号']
        self.密码 = config['密码']
        self.角色序号 = config['角色序号']
        self.代理配置 = config.get('代理配置', None) 
        self.proxies = None        
        self.cookies = None
        self.petId = None
        self.myUserId = None
        self.validateParam = None
        self.patterns = {
            'npc': re.compile(r"p\._getNpc\(([^)]*)\)"),
            'modify': re.compile(r'<[A-Za-z0-9 ="\':;/_\-.]*>'),
            'petLv': re.compile(r"petLv=(\d+);"),   # 宠物等级
            'nowMap': re.compile(r'nowMap\s*=\s*"([^"]+)"'),    # 当前所在地图
            'room': re.compile(r'room\s*=\s*"([^"]+)"'),    # 当前所在房间
            'hp_left': re.compile(r"<span id='hpLine_left_no'[^>]*>(\d+)</span>"),  # 剩余hp
            'hp_max': re.compile(r"<span id='hpLine_left_max_no'[^>]*>(\d+)</span>"),   # 最大hp
            'sp_left': re.compile(r"<span id='mpLine_left_no'[^>]*>(\d+)</span>"),  # 剩余sp
            'sp_max': re.compile(r"<span id='mpLine_left_max_no'>(\d+)</span>"),    # 最大sp
            'exp': re.compile(r'<\s*span\s+id\s*=\s*["\']?expBai["\']?\s*>(\d+)\s*</\s*span\s*>', re.IGNORECASE),   # 百分比经验
            'join_role': re.compile(r"selectimg\(\s*(\d+)\s*,\s*'click'\s*,\s*(\d+)[^)]*\)\s*.*?"
                                    r'<div\s+align="center"\s+style="position:relative;top:-10px">(.*?)</div>', re.DOTALL),  # 获取角色代码及序号部分
            # 'skills': re.compile(r"p\.cutArray\[(?:[0-5])\]='(perform.*?)';"),  #
            'petName': re.compile(r'petName\s?=\s?"(.*)";'),    # 宠物名
            'myUserId': re.compile(r'myUserId\s?=\s?"(.*)";'),  # id
            'validateParam': re.compile(r'validateParam\s?=\s?"(.*)";'),    # 链接验证参数
            'combat_start': re.compile(r"p\._combat\s*\(", re.IGNORECASE),      # 进入战斗
            'combat_end': re.compile(r"p\.lost\(\s*p\.petWin\.fighter_2\s*\)", re.IGNORECASE),  # 退出战斗
        }
        self.online_url = None
        self.online_thread = None
        self.战斗状态 = False
        self.最后进入战斗时间 = None
        self.Npc列表 = []
        self.房间 = None
        self.地图 = None
        self.重连间隔 = default_reconnect
        self.背包道具 = []
        self.背包道具更新时间 = None
        self.职业技能 = None
        self.技能列表 = []
        self.技能列表更新时间 = None
        self.等级 = None
        self.宠物名 = None
        self.hp_left = None
        self.sp_left = None
        self.hp_max = None
        self.sp_max = None
        self.经验百分比 = None
        self.最新命令 = None
        self.Npc对话框内容 = None
        self.任务列表 = None
        self.任务列表刷新时间 = None
        self.命令地址 = f'http://{self.服务器}.pet.imop.com/action.jsp?'
        self.日常拖把开关 = config.get('日常拖把开关', True)
        self.日常礼包开关 = config.get('日常礼包开关', True)
        self.日常竞技场开关 = config.get('日常竞技场开关', False)
        self.日常魔化开关 = config.get('日常魔化开关', False)
        self.活动如意开关 = config.get('活动如意开关', False)
        self.日常乐园开关 = config.get('日常乐园开关', False)
        self.日常银月开关 = config.get('日常银月开关', False)
        self.日常还猪开关 = config.get('日常还猪开关', False)
        self.日常沼泽开关 = config.get('日常沼泽开关', False)
        self.日常小镇开关 = config.get('日常小镇开关', False)
        self.活动通天开关 = config.get('活动通天开关', False)
        self.活动逆无双开关 = config.get('活动逆无双开关', False)
        self.活动罗汉开关 = config.get('活动罗汉开关', False)
        self.闲时任务 = config.get('闲时任务', None)
        self.刷怪配置 = config.get('刷怪配置', {})
        self.技能 = config.get('战斗技能', {})  # 保留兼容性，但将逐步废弃
        self.技能配置 = config.get('技能配置', {})  # 新的技能配置
        self.通天配置 = config.get('通天配置', {})  # 通天活动配置
        self.逆无双配置 = config.get('逆无双配置', {})  # 逆无双活动配置
        self.换装包前缀 = config.get('换装包前缀', None)
        self.今日已执行活动 = {}  # 记录今日已执行的活动任务
        self.自身状态 = None
        self.对手状态 = None
        self.通天当前层已击杀 = False
        self.命令线程锁 = Lock()
        self.道具说明信息 = None
        self.道具说明信息更新时间 = None
        self.近十条命令数组 = []
        self.近一百条左下框信息 = []
        self.近一百条右上框信息 = []
        self.近一百条右下框信息 = []
        self.登入标记 = False
        # 任务配置 - 便于维护和扩展
        self.日常任务配置 = [
            {
                '任务名': '拖把',
                '开关属性': 'self.日常拖把开关',
                '执行函数': self.日常_拖把,
                '优先级': 1
            },
            {
                '任务名': '魔化',
                '开关属性': 'self.日常魔化开关',
                '执行函数': self.日常_魔化,
                '优先级': 2
            }
        ]

        self.活动任务配置 = [
            {
                '任务名': '通天',
                '星期': [1, 5],  # 周二、周六
                '开始时间': datetime.time(19, 50),
                '结束时间': datetime.time(20, 59),
                '开关属性': 'self.活动通天开关',
                '执行函数': self.活动_通天,
                '优先级': 1
            },
            {
                '任务名': '逆无双',
                '星期': [0, 2, 4],  # 周一、三、五
                '开始时间': datetime.time(19, 55),
                '结束时间': datetime.time(20, 59),
                '开关属性': 'self.活动逆无双开关',
                '执行函数': self.活动_逆无双,
                '优先级': 2
            },
            {
                '任务名': '罗汉',
                '星期': [1, 3],  # 周二、四
                '开始时间': datetime.time(19, 0),
                '结束时间': datetime.time(19, 59),
                '开关属性': 'self.活动罗汉开关',
                '执行函数': self.活动_罗汉,
                '优先级': 3
            },
            {
                '任务名': '如意',
                '时间段': [
                    (datetime.time(12, 0), datetime.time(12, 29), '如意_12'),
                    (datetime.time(13, 40), datetime.time(13, 50), '如意_test'),    # 测试
                    (datetime.time(17, 30), datetime.time(17, 59), '如意_17'),
                    (datetime.time(21, 0), datetime.time(21, 29), '如意_21'),
                ],
                '开关属性': 'self.活动如意开关',
                '执行函数': self.活动_如意,
                '优先级': 0  # 最高优先级
            }
        ]

        self.闲时任务配置 = [
            {
                '任务名': '禅壹',
                '配置值': '闲时_禅壹',
                '执行函数': self.闲时_禅壹,
                '优先级': 1
            },
            {
                '任务名': '禅伍',
                '配置值': '闲时_禅伍',
                '执行函数': self.闲时_禅伍,
                '优先级': 2
            },
            {
                '任务名': '道壹',
                '配置值': '闲时_道壹',
                '执行函数': self.闲时_道壹,
                '优先级': 3
            },
            {
                '任务名': '打工',
                '配置值': '闲时_打工',
                '执行函数': self.闲时_打工,
                '优先级': 4
            },
            {
                '任务名': '刷怪',
                '配置值': '闲时_刷怪',
                '执行函数': self.闲时_刷怪,
                '优先级': 5
            }
        ]
        self.保持在线线程 = MyThread(target=self.pull_online, name=f'online-{self.账号}')
        self.保持在线线程.start()

        # 启动技能冷却线程
        self.技能冷却线程 = MyThread(target=self.通用_技能冷却, name=f'skill-cd-{self.账号}')
        self.技能冷却线程.start()

        # 启动技能释放线程
        self.技能释放线程 = MyThread(target=self.通用_技能释放, name=f'skill-cast-{self.账号}')
        self.技能释放线程.start()
        # self.pull_online()  # 初始化完了以后直接保持在线
    
    def 确认服务器登录状态(self):
        while True:
            print('获取代理配置')
            self.proxies = get_proxy(self.代理配置.get('代理账号'), self.代理配置.get('代理密码')) if self.代理配置 else None
            if self.proxies is not None:
                print('获取代理配置成功')
                _temp_frequency = 30
                _temp_sleep = 0.5
            else :
                print('获取代理配置失败')
                _temp_frequency = 30
                _temp_sleep = 1.51
            for i in range(_temp_frequency):
                res = requests.get(f'http://{self.服务器}.pet.imop.com')
                if res.status_code == 503:
                    print(f'服务器{self.服务器}维护未完成')
                    time.slee(_temp_sleep)
                else:
                    # print(res.status_code)
                    print(f'服务器{self.服务器}不在维护,可登录')
                    return True

    def login(self):
        # 登录错误码
        _error_code = {
            '0': "登录失败,请重新登录",
            '1': "签名验证失败",
            '2': "时间戳过期",
            '3': "参数为空或格式不正确",
            '4': "用户名密码验证未通过",
            '5': "用户已被锁定",
            '6': "密保未通过",
            '7': "cookie验证未通过",
            '8': "token验证未通过",
            '9': "大区验证未通过",
            '11': "验证码错误",
            '12': "验证码为空",
            '999': "系统异常，登录失败"
        }
        # 登录信息
        self.确认服务器登录状态()
        while True:
            # 登录发送信息
            self.proxies = get_proxy(self.代理配置.get('代理账号'), self.代理配置.get('代理密码')) if self.代理配置 else None
            login_post = {
                'url': f'http://{self.服务器}.pet.imop.com/LoginAction.jsp',
                'cookies': {'mopet_logon': '123'},
                'headers': {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Origin': f'http://{self.服务器}.pet.imop.com',
                    'Referer': f'http://{self.服务器}.pet.imop.com/login.html',
                    'Connection': 'keep-alive',
                    'User-Agent': 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 10.0; WOW64; Trident/7.0; .NET4.0C; .NET4.0E)',
                },
                'data': {
                    'user_name': quote(self.账号, encoding="gbk"),
                    'password': self.密码,
                    'checkcode': get_yzm(proxies=self.proxies)
                },
                'proxies': self.proxies,
            }
            # 登录返回信息
            login_response = requests.post(**login_post)
            if r'document.location="/pet.jsp"' in login_response.text:
                self.cookies = login_response.cookies
                print('登陆成功')
                return True
            else:
                for _code in _error_code.keys():
                    if f'errCode={_code}"' in login_response.text:
                        if _code == '11' or _code == '999':
                            # 错误码为11,验证码错误,重新获取验证码登录
                            # 错误码为999,封ip,重新获取验证码登录
                            continue
                        else:
                            print(f'出现非验证码错误,错误类型为=>{_error_code[_code]}')
                            return False

    def join_role(self):
        # 进入切换角色开始
        while True:
            changerole_get = {
                'url': f'http://{self.服务器}.pet.imop.com/action.jsp?action=changerole',
                'cookies': self.cookies,
                'headers': {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Origin': f'http://{self.服务器}.pet.imop.com',
                        'Referer': f'http://{self.服务器}.pet.imop.com/login.html',
                        'Connection': 'keep-alive',
                        'User-Agent': 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 10.0; WOW64; Trident/7.0; .NET4.0C; .NET4.0E)',
                },
                # 'proxies': self.proxies,
            }
            changerole_response = requests.get(**changerole_get)
            if 'selectPet.jsp' in changerole_response.text:
                break
        while True:
            join_get = {
                'url': f'http://{self.服务器}.pet.imop.com/pet.jsp',
                'cookies': self.cookies,
                'headers': {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Origin': f'http://{self.服务器}.pet.imop.com',
                        'Referer': f'http://{self.服务器}.pet.imop.com/login.html',
                        'Connection': 'keep-alive',
                        'User-Agent': 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 10.0; WOW64; Trident/7.0; .NET4.0C; .NET4.0E)',
                },
            }
            join_response = requests.get(**join_get)
            if 'selectimg' in join_response.text:
                break
        # 进入切换角色完成
        role_boxes = self.patterns['join_role'].findall(join_response.text)
        for role in role_boxes:
            print(f"""角色序号[{role[0]}]角色名[{role[2]}]角色id[{role[1]}]""")
        for role in role_boxes:
            if int(role[0]) == int(self.角色序号):
                self.petId = role[1]
                _pet_url = f'http://{self.服务器}.pet.imop.com/pet.jsp?petid={self.petId}'
                break
        # petId获取成功
        # 进入角色
        while True:
            role_get = {
                'url': f'http://{self.服务器}.pet.imop.com/pet.jsp?petid={self.petId}',
                'cookies': self.cookies,
            }
            role_response = requests.get(**role_get)
            if 'showWorldMap' in role_response.text:
                break
        # 进入角色完成
        print('进入角色完成')
        # 初始化属性
        self.refresh_info()
        return True

    def refresh_info(self):
        while True:
            response = requests.get(url=f'http://{self.服务器}.pet.imop.com/pet.jsp?petid={self.petId}', cookies=self.cookies)
            t = response.text
            try:
                self.等级 = self.patterns['petLv'].search(t).group(1)
                self.地图 = self.patterns['nowMap'].search(t).group(1)
                self.房间 = self.patterns['room'].search(t).group(1)
                self.宠物名 = self.patterns['petName'].search(t).group(1)
                self.myUserId = self.patterns['myUserId'].search(t).group(1)
                self.validateParam = self.patterns['validateParam'].search(t).group(1)
                self.hp_left = self.patterns['hp_left'].search(t).group(1)
                self.sp_left = self.patterns['sp_left'].search(t).group(1)
                self.hp_max = self.patterns['hp_max'].search(t).group(1)
                self.sp_max = self.patterns['sp_max'].search(t).group(1)
                self.经验百分比 = int(re.compile(r'<\s*span\s+id\s*=\s*["\']?expBai["\']?\s*>(\d+)\s*</\s*span\s*>', re.IGNORECASE).search(t).group(1))
                self.online_url = f'http://{self.服务器}.pet.imop.com:8080/io/{self.myUserId}&{self.validateParam}'
                print('初始化属性')
                return
            except:
                pass

    def pull_online(self):
        def _pull_online():
            while True:
                online_get = {
                    'url': self.online_url,
                    'cookies': self.cookies,
                    'stream': True,
                }
                try:
                    with requests.get(**online_get) as response:
                        self.send_orders('look', 0) # 连接上以后先发送look,刷新一下场景
                        buffer = ""
                        self.登入标记 = True
                        print('保持在线')
                        for chunk in response.iter_content(chunk_size=1, decode_unicode=True):
                            if chunk:
                                # 确保chunk是字符串类型
                                if isinstance(chunk, bytes):
                                    chunk = chunk.decode('utf-8', errors='ignore')
                                elif chunk is None:
                                    continue
                                buffer += str(chunk)
                                # 检查是否有完整的<script>标签
                                while '<script>' in buffer and '</script>' in buffer:
                                    start = buffer.find('<script>')
                                    end = buffer.find('</script>') + len('</script>')
                                    if start < end:
                                        script_content = buffer[start:end]
                                        buffer = buffer[end:]
                                        # 提取<script>标签内的内容
                                        inner_content = script_content[8:-9]  # 去掉<script>和</script>
                                        if inner_content.strip():
                                            _function_list = parse_js_code(inner_content)# 解析js代码
                                            for _func in _function_list:
                                                # print(_func)
                                                match _func['function_name']:
                                                    case _ if _func['function_name'] in [
                                                        # 不处理函数列表
                                                        'initWorker',   # 初始化函数
                                                        'cls',          # 清空房间
                                                        'cps',          # 清空房间
                                                        'offOpenWin',   # 关闭窗口
                                                        'clsMes',       # Npc对话框清空
                                                        '_roomDesc',     # 房间描述
                                                        'reOnlineNum',  # 在线状态
                                                        'addUser',      # 当前房间增加角色
                                                        'delUser',      # 当前房间删除角色  
                                                        'showRen',      # Npc图片
                                                        'closeRen',     # Npc图片
                                                        'showAlert',    # 白底小提示框
                                                        'win',          # 战斗胜利,但是太快反应会卡指令,所以不作为结束战斗依据
                                                        'closeBossZhanDouInfo',     # 疑似战斗结束,但是不能用,同上
                                                        '_showMiracle', # 无关痛痒,不知道是啥
                                                        'showRenPic',   # NPC图片
                                                        'showRenBigPic',   # NPC图片
                                                        '_look',        # 被人观察,不处理
                                                        'lost',         # 伪脱战,不处理
                                                        '_combat',      # 原始判断进战
                                                    ]:
                                                        continue
                                                    case 'addCM':
                                                        局_信息 = get_time_str() + '=>' + _func['parameters'][0]['value']
                                                        self.近一百条右上框信息.append(局_信息)
                                                        print(f'CM右上框:{局_信息}')
                                                        if len(self.近一百条右上框信息) > 100:
                                                            self.近一百条右上框信息.pop(0)                                                       
                                                    case 'addRM':
                                                        局_信息 = get_time_str() + '=>' + _func['parameters'][0]['value']
                                                        self.近一百条左下框信息.append(局_信息)
                                                        print(f'RM左下框:{局_信息}')
                                                        if len(self.近一百条左下框信息) > 100:
                                                            self.近一百条左下框信息.pop(0)
                                                    case 'addMY':
                                                        局_信息 = get_time_str() + '=>' + _func['parameters'][0]['value']
                                                        self.近一百条右下框信息.append(局_信息)
                                                        print(f'MY右下框:{局_信息}')
                                                        if len(self.近一百条右下框信息) > 100:
                                                            self.近一百条右下框信息.pop(0)
                                                    case 'addMessage':
                                                        match _func['parameters'][0]['value']:
                                                            case 'roomReader':
                                                                局_信息 = get_time_str() + '=>' + _func["parameters"][1]["value"]
                                                                if '你向' in 局_信息 and '发起攻击!' in 局_信息:
                                                                    self.战斗状态 = True
                                                                    self.最后进入战斗时间 = time.time()
                                                                    print('进入战斗')
                                                                print(f'Message左下框:{局_信息}')
                                                                self.近一百条左下框信息.append(局_信息)
                                                                print(f'RM左下框:{局_信息}')
                                                                if len(self.近一百条左下框信息) > 100:
                                                                    self.近一百条左下框信息.pop(0)
                                                            case _:
                                                                print('待处理addMessage')
                                                    # case '_combat':
                                                    #     self.战斗状态 = True
                                                    #     self.最后进入战斗时间 = time.time()
                                                    #     print('进入战斗')
                                                    # case 'lost':
                                                    #     self.战斗状态 = False
                                                    #     self.最后进入战斗时间 = None
                                                    #     print('结束战斗')
                                                    case '_showFightStatus':
                                                        if _func['parameters'][0]['value'] == 'fighter_1_status':
                                                            self.自身状态 = _func['parameters'][1]['value']
                                                            print(f'战斗-自身状态:{self.自身状态}')
                                                        if _func['parameters'][0]['value'] == 'fighter_2_status':
                                                            self.对手状态 = _func['parameters'][1]['value']
                                                            print(f'战斗-对手状态:{self.对手状态}')
                                                    case 'state':
                                                        print('战斗相关,暂不处理')
                                                    case _ if _func['function_name'] in ['att1', 'att2']:
                                                        print('战斗相关,暂不处理')
                                                    case 'addNpcs':
                                                        self.战斗状态 = False
                                                        self.自身状态 = None    # 脱战清空状态
                                                        self.对手状态 = None    # 脱战清空状态
                                                        _s = _func['parameters'][0]['value']
                                                        self.Npc列表 = [i.replace("'", '').replace(" ", '').split(',') for i in self.patterns['npc'].findall(_s)]
                                                    case 'setRoom':
                                                        self.房间 = _func['parameters'][0]['value']
                                                        # self.战斗状态 = False
                                                        # self.自身状态 = None    # 脱战清空状态
                                                        # self.对手状态 = None    # 脱战清空状态
                                                        print('房间', self.房间)
                                                    case 'changeMap':
                                                        self.地图 = _func['parameters'][0]['value']
                                                        print('地图', self.地图)
                                                    case 'setMaxHP':
                                                        self.hp_max = _func['parameters'][0]['value']
                                                        # print(f'最大hp:{self.hp_max}')
                                                    case 'setMaxSP':
                                                        self.sp_max = _func['parameters'][0]['value']
                                                        # print(f'最大sp:{self.sp_max}')
                                                    case 'setLine':
                                                        match _func['parameters'][0]['value']:
                                                            case 'hpLine_left':
                                                                局_数据 = _func['parameters'][-1]['value']
                                                                if 局_数据 == '-':
                                                                    continue
                                                                self.hp_left = int(局_数据)
                                                            case 'mpLine_left':
                                                                局_数据 = _func['parameters'][-1]['value']
                                                                if 局_数据 == '-':
                                                                    continue
                                                                self.sp_left = int(_func['parameters'][-1]['value'])
                                                            case 'hpLine_right':
                                                                pass
                                                            case 'mpLine_right':
                                                                pass
                                                    case 'beiDing':
                                                        print(f'被顶号:ip{_func["parameters"][0]['value']},延迟{self.重连间隔}秒后重登')
                                                        time.sleep(self.重连间隔)
                                                    case 'setFightTaskImg':
                                                        # print(_func)
                                                        # 无效,这是左上角的图标.不作为释放技能参考,且无法准确判断技能,如十字斩和强打是一个图标
                                                        continue
                                                    case 'showLeftTalk':
                                                        局_技能名称 = _func['parameters'][0]['value'][:-1]
                                                        self.技能配置[局_技能名称]['剩余CD'] = self.技能配置[局_技能名称]['CD'] + 1
                                                    case 'showRightTalk':
                                                        print(_func)
                                                    case _ if _func['function_name'] in ['showI', 'showIHide']:
                                                        self.背包道具 = ret_packages(_func)
                                                        self.背包道具更新时间 = time.time()
                                                    case '_skillsubs':
                                                        # print(_func['parameters'][0]['value'])
                                                        self.技能列表 = eval(_func['parameters'][0]['value'].replace('false', 'False').replace('true','True'))
                                                        self.技能列表更新时间 = time.time()
                                                    case 'setLv':
                                                        self.等级 = int(_func['parameters'][0]['value'])
                                                        # print(self.等级)
                                                    case 'setExp':
                                                        self.经验百分比 = int(round(int(_func['parameters'][0]['value'])/int(_func['parameters'][2]['value']),4) * 100)
                                                        # TODO 可以在这里卡一下经验
                                                        # print(self.经验百分比)
                                                    case '_petinfo':
                                                        # 宠物信息
                                                        # for i in _func['parameters']:
                                                        #     del i['raw']
                                                        #     print(i)
                                                        continue
                                                    case 'showAllotWin':
                                                        # 新建一个线程进行道具需求,防止阻塞
                                                        # print('分配道具函数处理')
                                                        # _s = _func['parameters'][0]['value']
                                                        # _number = re.findall(r'p\.allotObj\((\d+)', _s)
                                                        # for i in _number:
                                                        #     # 结尾2是需求, 1是放弃
                                                        #     self.send_orders(f'foo rank allot {i} 2')
                                                        continue
                                                    case 'addNPCC':
                                                        self.Npc对话框内容 = _func['parameters'][0]['value']
                                                        print('Npc对话框:', self.Npc对话框内容)
                                                    case 'showTask':
                                                        _t = _func['parameters'][0]['value']
                                                        self.任务列表 = js_to_python(_t)
                                                        self.任务列表刷新时间 = time.time()
                                                    case 'showItemDesc':
                                                        self.道具说明信息 = {
                                                            'name': _func['parameters'][0]['value'],
                                                            'description': _func['parameters'][2]['value']
                                                        }
                                                        self.道具说明信息更新时间 = time.time()
                                                    case 'yDeal':
                                                        # 这里是交易
                                                        continue
                                                    case _:
                                                        print('未处理格式')
                                                        print(_func)
                finally:
                    pass
        if self.login():
            if self.join_role():
                # self.online_thread = MyThread(target=_pull_online, name=f'online-{self.账号}')
                # self.online_thread.start()
                _pull_online()
    
    def send_orders(self, orders:str, delay=None):
        with self.命令线程锁:
            orders = orders.split('|')
            # print(orders)
            for order in orders:
                if order == '':
                    continue
                try:
                    self.最新命令 = order
                    # 更新近十条命令数组
                    self.近十条命令数组.append(order)
                    if len(self.近十条命令数组) > 10:
                        self.近十条命令数组.pop(0)

                    requests.post(
                        url=self.命令地址,
                        cookies=self.cookies,
                        data={
                            'action': 'inputCommand',
                            'inputCommand': ('/' + order).encode('gbk'),
                        }
                    )
                except Exception as e:
                    print(e)
                if delay is None:
                    time.sleep(self.延迟)
                else:
                    time.sleep(delay)
    
    def gto(self, room):
        while True:
            if self.地图 is None or self.房间 is None:
                self.refresh_info()
            寻路起点 = self.地图 + '|' + self.房间
            print(f'从{寻路起点}到{room}')
            _way = ret_way(寻路起点, room)
            print(_way)
            self.send_orders(f'relive|look|{_way}')
            if 寻路起点 == room:
                break

    def 通用_技能释放(self):
        """通用技能释放管理 - 根据当前状态自动选择合适的技能配置进行释放"""
        # 初始化技能剩余CD - 使用新的技能配置
        for key in self.技能配置.keys():
            self.技能配置[key]['剩余CD'] = 0

        while True:
            # 最外层循环,保证一直执行
            if self.战斗状态:
                # 动态获取当前应该使用的技能释放列表
                局_技能释放列表 = self._获取当前技能释放列表()

                # 遍历技能释放列表
                for 局_技能名称 in 局_技能释放列表:
                    if not self.战斗状态:
                        break
                    # 在技能配置中查找该技能
                    if 局_技能名称 in self.技能配置:
                        局_技能属性 = self.技能配置[局_技能名称]
                        # 检查CD和状态条件
                        if 局_技能属性['剩余CD'] <= 0 and (self.自身状态 is None or ('昏迷' not in self.自身状态) and ('沉默' not in self.自身状态)):
                            self.send_orders(f'perform {局_技能名称}')
                            time.sleep(局_技能属性['前摇'])     # 前摇抬手释放了再计算冷却
                            # 局_技能属性['剩余CD'] = 局_技能属性['CD']     # 冷却由showLeftTalk重制
                            time.sleep(局_技能属性['后摇'])
            time.sleep(0.1) # 防止CPU占用过高

    def _获取当前技能释放列表(self):
        """根据当前任务类型和状态，动态获取应该使用的技能释放列表"""
        # 闲时刷怪：使用刷怪配置
        if self.闲时任务 == '闲时_刷怪' and '释放技能列表' in self.刷怪配置:
            return self.刷怪配置['释放技能列表']

        # 活动通天：使用通天配置
        elif '通天塔' in str(self.房间) and '释放技能列表' in self.通天配置:
            return self.通天配置['释放技能列表']

        # 活动逆无双：使用逆无双配置
        elif '逆无双' in str(self.房间) and '释放技能列表' in self.逆无双配置:
            return self.逆无双配置['释放技能列表']

        # TODO: 在这里可以轻松添加其他活动的技能配置
        # elif '罗汉' in str(self.房间) and '释放技能列表' in self.罗汉配置:
        #     return self.罗汉配置['释放技能列表']

        # 兼容旧配置：如果没有新配置，使用旧的技能字典
        else:
            return list(self.技能.keys()) if self.技能 else []
            
    def 通用_技能冷却(self):
        """通用技能冷却管理 - 每秒减少所有技能的剩余CD"""
        while True:
            # 使用新的技能配置进行CD冷却
            for key, value in self.技能配置.items():
                if value['剩余CD'] > 0:
                    value['剩余CD'] -= 1
            time.sleep(1)

    def 日常_拖把(self):
        _markpoint = self.地图 + '|' + self.房间        # 标记当前位置
        self.gto('map.mopcity|驿站')
        self.send_orders(daily_tb_orders)
        self.gto(_markpoint)        # 回到标记点

    def 日常_魔化(self):
        _markpoint = self.地图 + '|' + self.房间        # 标记当前位置
        self.gto('map.jixiedao|机械岛接待室')
        self.send_orders(daily_mh_orders)
        self.gto(_markpoint)

    def 活动_如意(self):
        _markpoint = self.地图 + '|' + self.房间        # 标记当前位置
        self.gto('map.mopcity|添香楼')
        self.send_orders('getrybp 2')
        self.gto(_markpoint)

    def 闲时_禅壹(self):
        if f'{self.地图}|{self.房间}' != 'chan|禅壹':
            self.gto('chan|禅壹')
        self.send_orders('fetchtimeprize')

    def 闲时_禅伍(self):
        if f'{self.地图}|{self.房间}' != 'chan|禅伍':
            self.gto('chan|禅伍')
        self.send_orders('fetchtimeprize')
    
    def 闲时_道壹(self):
        if f'{self.地图}|{self.房间}' != 'dao|道壹':
            self.gto('dao|道壹')
        self.send_orders('fetchtimeprize')
    
    def 闲时_打工(self):
        if f'{self.地图}|{self.房间}' != 'map.all.other.GongChang|工厂':
            self.gto('map.all.other.GongChang|工厂')
        self.send_orders('fetchtimeprize')
    
    def 闲时_刷怪(self):
        def _刷怪_单次刷怪():
            # 检查是否有目标怪物
            局_Npc列表拷贝 = self.Npc列表.copy()
            局_Npc列表拷贝.reverse()        # 逆转列表,逆序开怪
            for 局_npc in 局_Npc列表拷贝:    # 对当前怪物房间遍历
                for 局_target in self.刷怪配置['刷怪目标']:
                    if (局_target in 局_npc[1] and
                        局_npc[3].find('尸体') < 0 and
                        局_npc[3].find('活动') < 0 and
                        局_npc[3].find('BOSS') < 0 and
                        局_npc[3].find('战斗中') < 0):    # 如果目标在怪物名称里
                        match self.刷怪配置.get('伏击技能', None):
                            case None:
                                局_开怪命令 = f'bar34 {局_npc[0]} {局_npc[2]}'
                            case _:
                                局_开怪命令 = f'perform {self.刷怪配置["伏击技能"]} {局_npc[0]}'
                        if 局_开怪命令 == self.最新命令:
                            continue
                        self.send_orders(局_开怪命令)
                        return True
            return False        

        def _刷怪_清空房间():
            while True:
                if _刷怪_单次刷怪():
                    while self.战斗状态:
                        time.sleep(0.1)
                else:
                    break

        # 判断配置开始,检测刷怪配置是否完整,如果不完整的话,切换为禅壹
        if self.刷怪配置 is None:
            print('无刷怪配置,切换闲时任务为禅壹')
            self.闲时任务 = '闲时_禅壹'
            return
        if self.刷怪配置.get('刷怪起点') is None:
            print('无刷怪起点,切换闲时任务为禅壹')
            self.闲时任务 = '闲时_禅壹'
            return
        if self.刷怪配置.get('刷怪路线') is None:
            print('无刷怪路线,切换闲时任务为禅壹')
            self.闲时任务 = '闲时_禅壹'
            return
        if self.刷怪配置.get('刷怪目标') is None:
            print('无刷怪目标,切换闲时任务为禅壹')
            self.闲时任务 = '闲时_禅壹'
            return
        # 判断配置结束        

        # 前置行为,装备技能,装备武器,装备店修理,修女治疗
        # 更新当前装备技能
        self.功能_装备指定技能(self.刷怪配置['职业技能'])
        # 更新当前武器
        self.功能_装备指定武器(self.换装包前缀, self.刷怪配置['刷怪武器'])
        # 前置行为装备店修理,修女治疗
        self.gto('map.maoyin|装备店')
        self.send_orders('xiuli all|gto 教堂|zhiliao queding')
        
        self.gto(self.刷怪配置['刷怪起点'])
        #  初始化刷怪路线
        局_实用路线对象 = MyjRoute(self.刷怪配置['刷怪路线'])
        局_实用路线对象.now = 局_实用路线对象.head
        局_处理道具圈数 = self.刷怪配置.get('跑图多少圈进行一次售卖', 50)
        # 回到刷怪起点
    

        # 循环刷怪开始
        局_处理道具圈数计数 = 0
        while True:            
            # 当前房间并非路线的最后一间,默认走到下一个节点
            if 局_实用路线对象.now  != 局_实用路线对象.tail:
                局_实用路线对象.now = 局_实用路线对象.now.next  # 地图节点切换到下一个节点
                # print(f'下一房间{局_实用路线对象.now.room}')
                尝试次数 = 0  # 修复：将计数器移到循环外部
                while True:
                    if 尝试次数 > 10:
                        print(f'房间移动失败超过10次，重置到刷怪起点')
                        self.gto(self.刷怪配置['刷怪起点'])
                        局_实用路线对象.now = 局_实用路线对象.head
                        break
                    self.send_orders(f'gto {局_实用路线对象.now.room}') # 走过去
                    if self.房间 == 局_实用路线对象.now.room:
                        break
                    else:
                        尝试次数 += 1
                        print(f'房间移动失败，尝试次数：{尝试次数}')
                        self.send_orders('look')
                _刷怪_清空房间()
            else:
                # 当前房间是路线的最后一间
                # 在这里判断圈数,进行处理背包
                if 局_处理道具圈数计数 < 局_处理道具圈数:
                    局_处理道具圈数计数 += 1                    # 没到清理圈数,计数+1
                else:
                    局_标记位置 = f'{self.地图}|{self.房间}'    # 标记当前位置
                    self.gto('map.maoyin|装备店')
                    while (self.背包道具更新时间 is None) or (len(self.背包道具) == 0) or (time.time() - self.背包道具更新时间 > 3):
                        self.send_orders('i', 1)
                    局_背包道具列表 = self.背包道具.copy()
                    局_长售卖代码 = ''
                    局_背包道具列表.reverse()
                    for 局_道具 in 局_背包道具列表:
                        for 局_需要处理的物品开头名称 in self.刷怪配置['售卖道具列表']:
                            if 局_道具['name'].startswith(局_需要处理的物品开头名称):
                                # 确保index和quantity不为None
                                index = 局_道具.get('index', '')
                                quantity = 局_道具.get('quantity', '')
                                if index and quantity:
                                    局_长售卖代码 += f'sell {index} {quantity} OK|'
                                break
                    self.send_orders(局_长售卖代码)
                    self.send_orders('xiuli all|gto 教堂|zhiliao queding')
                    局_长售卖代码 = ''
                    self.gto(局_标记位置)                       # 回到标记点
                    局_处理道具圈数计数 = 0
                # 背包处理完成                
                局_实用路线对象.now = 局_实用路线对象.head      # 地图节点切换到头结点
                # 跑图完成一轮,回到起点之前执行额外命令
                if self.刷怪配置.get('每圈结束执行的其他代码') is not None:
                    self.send_orders(self.刷怪配置['每圈结束执行的其他代码'])
                # 回到起点
                if self.房间 == 局_实用路线对象.tail.room:
                    while True:
                        self.send_orders(f'gto {局_实用路线对象.head.room}') # 走过去
                        if self.房间 == 局_实用路线对象.head.room:
                            break
                        else:
                            self.send_orders('look')
                else:    
                    self.gto(self.刷怪配置['刷怪起点'])
                    self.send_orders('fetchtimeprize')                      # 领取每日时间礼包
            # 进入清理怪物函数
            
    def 活动_通天(self):
        # 更新当前装备技能
        self.功能_装备指定技能(self.通天配置['职业技能'])
        self.功能_装备指定武器(self.换装包前缀, self.通天配置['刷怪武器'])
        
        while True:
            # 当前房间不包含通天塔字样,不在指定位置
            if '通天塔' not in self.房间:
                self.gto('map.baimagang|通天塔')
            # 当前房间在通天塔门口
            if self.房间 == '通天塔':
                self.send_orders('comein yes', 0.25)
                continue
            if '通天塔' in self.房间 and '层' in self.房间:
                self.通天当前层已击杀 = False
                while True:
                    局_NPC逆序列表 = [i for i in self.Npc列表.copy() if ('尸体' not in i[3] and '战斗中' not in i[3])] 
                    if len(局_NPC逆序列表) > 0:
                        局_NPC逆序列表.reverse()
                        self.send_orders(f'bar34 {局_NPC逆序列表[0][0]} {局_NPC逆序列表[0][2]}', 0.25)
                        while self.战斗状态:
                            self.通天当前层已击杀 = True
                            time.sleep(0.1)
                        if self.通天当前层已击杀:
                            self.通天当前层已击杀 = False
                            局_房间名 = self.房间
                            while self.房间 == 局_房间名:
                                self.send_orders('gotonext')

    def 活动_逆无双(self):
        self.功能_装备指定技能(self.逆无双配置['职业技能'])
        self.功能_装备指定武器(self.换装包前缀, self.逆无双配置['刷怪武器'])
        局_杀怪数量字典 = {
            1:5,
            2:5,
            3:10,
            4:10,
            5:15,
            6:15,
            7:20,
            8:20,
            9:25,
            10:30
        }
        if '通天塔' not in self.房间:
            self.gto('map.baimagang|通天塔')        # 不在逆无双入口,走到白马-通天塔
        while self.房间 == '通天塔':
            self.send_orders('comeinNWST yes', 0.25)
        bar_code = ''   # 初始化一个空字符串,用作打BOSS用
        while (bar_code != 'tiaozhan地穴领主') and ('逆无双' in self.房间):   # 当打BOSS不是地穴领主时,继续循环
            if '逆无双' in self.房间 and '层' in self.房间:
                # 当前在塔里
                # 获取当前所在层
                局_文本 = self.房间[5:7]
                if '层' in 局_文本:
                    局_当前层 = int(局_文本[:-1])
                else:
                    局_当前层 = int(局_文本)
                局_当前房间尾号 = int(self.房间[-2:])
                局_楼层前缀 = self.房间[:-2]
                if 局_杀怪数量字典[局_当前层] > 0:  # 仍需击杀小怪
                    局_怪物列表 = [怪物 for 怪物 in self.Npc列表.copy() if ('尸体' not in 怪物[3] and '战斗中' not in 怪物[3])]
                    if len(局_怪物列表) > 0:    # 当前房间有怪
                        局_怪物列表.reverse()   # 逆序
                        for 局_怪物 in 局_怪物列表:
                            self.send_orders(f'bar34 {局_怪物[0]} {局_怪物[2]}')
                            if self.战斗状态:
                                局_杀怪数量字典[局_当前层] -= 1
                                while self.战斗状态:
                                    time.sleep(0.1)
                                break
                    else:                       # 当前房间没怪
                        if 局_当前房间尾号 >= 10:
                            self.send_orders(f'gto {局_楼层前缀}01')   # 回到01
                        else:   # 尾号小于10
                            self.send_orders(f'gto {局_楼层前缀}{局_当前房间尾号 + 1:02}')
                        continue    # 走完房间以后continue一下,进入下一个循环,下一个循环应该是继续监测怪物
                else:           # 数量字典值为0,代表当前层杀够数量了
                    self.send_orders(f'gto {self.房间[:-2]}16') # 走到BOSS房间
                    match 局_当前层:
                        case 1:
                            bar_code = 'tiaozhan露西法的镜像'
                        case 2:
                            bar_code = 'tiaozhan小晴的镜像'
                        case 3:
                            bar_code = 'tiaozhan血魔王'
                        case 4:
                            bar_code = 'tiaozhan吸血魔王'
                        case 5:
                            bar_code = 'tiaozhan地狱骑士'
                        case 6:
                            bar_code = 'tiaozhan月亮上的少女'
                        case 7:
                            bar_code = 'tiaozhan魔龙人'
                        case 8:
                            bar_code = 'tiaozhan加湿用烘干器'
                        case 9:
                            bar_code = 'tiaozhan波瑞阿斯.冰暴'
                        case 10:
                            bar_code = 'tiaozhan地穴领主'
                    while not self.战斗状态:        # 进入战斗
                        self.send_orders(bar_code)      
                    while self.战斗状态:            # 等待战斗结束
                        time.sleep()
                    局_房间 = self.房间
                    while self.房间 == 局_房间:
                        self.send_orders('upstairs')    # 打完怪,上楼  
    
    def 活动_罗汉(self):
        pass

    def 特殊_大懒怪(self):
        while len(self.Npc列表) == 0:
            time.sleep(0.1)
        for i in self.Npc列表.copy():
            if '树蛙' in i[1]:
                self.send_orders(f'bar34 {i[0]} {i[2]}')
                break
        keyboard.wait(';')

    def 功能_装备指定技能(self, 技能名称):
        # 更新当前装备技能
        while self.技能列表更新时间 is None or (time.time() - self.技能列表更新时间 > 5):
            self.send_orders('subs')
            for 局_技能列表 in self.技能列表:
                if 局_技能列表[0] == 技能名称:
                    if 局_技能列表[2][-2] is False:
                        print(f'未装备{技能名称},发送装备命令')
                        self.send_orders(f'equipskills {技能名称}')
                    else:
                        print(f'正在装备技能:{技能名称}')

    def 功能_装备指定武器(self, 换装包名称, 武器名称):
        if 换装包名称 is not None:
            while self.背包道具更新时间 is None or (time.time() - self.背包道具更新时间 > 5):        
                self.send_orders('i', 1)
            for 局_背包道具 in self.背包道具:
                if 局_背包道具['name'].startswith(换装包名称):
                    while self.道具说明信息更新时间 is None or (time.time() - self.道具说明信息更新时间 > 5):
                        self.send_orders(f'helptools {局_背包道具['index']}')
                    if self.道具说明信息['name'].startswith(换装包名称) and 武器名称 in self.道具说明信息['description']:
                        self.send_orders(f'use {局_背包道具['index']} equipall')
                    else:
                        print(f'{换装包名称}中无目标{武器名称},默认已装备')

    def 主函数(self):
        """
        主任务调度函数 - 基于时间驱动的任务管理
        """

        def 执行日常任务():
            """执行所有启用的日常任务"""
            print('=== 开始执行日常任务 ===')

            # 根据配置获取需要执行的日常任务
            需要执行的任务 = []
            for 任务配置 in self.日常任务配置:
                任务名 = 任务配置['任务名']
                开关属性 = 任务配置['开关属性']
                任务函数 = 任务配置['执行函数']
                优先级 = 任务配置['优先级']

                # 检查开关是否启用
                if eval(开关属性):
                    需要执行的任务.append((优先级, 任务名, 任务函数))

            # 按优先级排序执行
            需要执行的任务.sort(key=lambda x: x[0])

            # 依次执行日常任务
            for 优先级, 任务名, 任务函数 in 需要执行的任务:
                print(f'执行日常任务: {任务名} (优先级: {优先级})')
                try:
                    任务函数()
                    print(f'日常任务 {任务名} 完成')
                except Exception as e:
                    print(f'日常任务 {任务名} 执行异常: {e}')

            print('=== 日常任务全部完成 ===')

        def 检查活动时间():
            """检查当前是否为活动时间，返回活动名称和函数，或None"""
            现在 = datetime.datetime.now()
            当前星期 = 现在.weekday()  # 0=周一, 6=周日
            当前时间 = 现在.time()
            今天 = 现在.date().isoformat()

            # 收集所有可执行的活动任务
            可执行活动 = []

            for 任务配置 in self.活动任务配置:
                任务名 = 任务配置['任务名']
                任务函数 = 任务配置['执行函数']
                优先级 = 任务配置['优先级']

                if 任务名 == '如意':
                    # 如意活动特殊处理
                    开关属性 = 任务配置.get('开关属性')
                    if 开关属性 and eval(开关属性):
                        时间段列表 = 任务配置['时间段']
                        for 开始时间, 结束时间, 时间段标识 in 时间段列表:
                            if 开始时间 <= 当前时间 <= 结束时间:
                                执行标识 = f"{今天}_{时间段标识}"
                                if 执行标识 not in self.今日已执行活动:
                                    可执行活动.append((优先级, 时间段标识, 任务函数))
                                break
                else:
                    # 其他活动任务
                    开关属性 = 任务配置.get('开关属性')
                    if 开关属性 and eval(开关属性):
                        允许星期 = 任务配置['星期']
                        开始时间 = 任务配置['开始时间']
                        结束时间 = 任务配置['结束时间']

                        if (当前星期 in 允许星期 and 开始时间 <= 当前时间 <= 结束时间):
                            执行标识 = f"{今天}_{任务名}"
                            if 执行标识 not in self.今日已执行活动:
                                可执行活动.append((优先级, 任务名, 任务函数))

            # 按优先级排序，返回最高优先级的活动
            if 可执行活动:
                可执行活动.sort(key=lambda x: x[0])
                优先级, 活动名, 任务函数 = 可执行活动[0]
                return (活动名, 任务函数)

            return None

        def 是否日期切换时间():
            """检查是否为日期切换时间 (23:50-0:10)"""
            现在 = datetime.datetime.now()
            当前时间 = 现在.time()

            # 23:50-23:59 或 0:00-0:10
            if (datetime.time(23, 50) <= 当前时间 <= datetime.time(23, 59) or
                datetime.time(0, 0) <= 当前时间 <= datetime.time(0, 10)):
                return True
            return False

        time.sleep(5)
        # 主循环 - 每天的任务周期
        while True:
            try:
                print(f'\n=== 新的一天开始 {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")} ===')

                # 清空今日已执行活动记录
                self.今日已执行活动.clear()
                time.sleep(5)
                # 1. 执行日常任务
                执行日常任务()

                # 2. 进入时间段循环
                当前闲时线程 = None

                while True:
                    # 检查是否为日期切换时间
                    if 是否日期切换时间():
                        print('进入日期切换时间，暂停所有任务...')
                        if 当前闲时线程 and 当前闲时线程.is_alive():
                            当前闲时线程.destroy()
                            当前闲时线程 = None

                        # 等待到0:11再继续
                        while 是否日期切换时间():
                            time.sleep(30)  # 每30秒检查一次

                        print('日期切换完成，进入新的一天')
                        break  # 跳出时间段循环，进入新一天

                    # 检查活动时间
                    活动任务 = 检查活动时间()

                    if 活动任务:
                        活动名, 活动函数 = 活动任务

                        # 停止闲时任务
                        if 当前闲时线程 and 当前闲时线程.is_alive():
                            print(f'停止闲时任务，准备执行活动: {活动名}')
                            当前闲时线程.destroy()
                            当前闲时线程 = None

                        # 执行活动任务 - 线程化执行，避免阻塞主调度循环
                        print(f'开始执行活动任务: {活动名}')

                        def _执行活动任务():
                            try:
                                活动函数()
                                print(f'活动任务 {活动名} 完成')

                                # 标记该活动今日已执行
                                今天 = datetime.datetime.now().date().isoformat()
                                执行标识 = f"{今天}_{活动名}"
                                self.今日已执行活动[执行标识] = True

                            except Exception as e:
                                print(f'活动任务 {活动名} 执行异常: {e}')

                        # 创建活动任务线程
                        活动线程 = MyThread(target=_执行活动任务, name=f'activity-{活动名}')
                        活动线程.start()

                    else:
                        # 非活动时间，执行闲时任务
                        if not 当前闲时线程 or not 当前闲时线程.is_alive():
                            # 优化：直接创建闲时任务线程，避免执行闲时任务()函数的阻塞
                            if self.闲时任务:
                                # 根据配置查找对应的闲时任务
                                for 任务配置 in self.闲时任务配置:
                                    配置值 = 任务配置['配置值']
                                    任务名 = 任务配置['任务名']
                                    任务函数 = 任务配置['执行函数']

                                    if self.闲时任务 == 配置值:
                                        print(f'开始执行闲时任务: {任务名} ({配置值})')
                                        当前闲时线程 = MyThread(target=任务函数, name=f'idle-{任务名}')
                                        当前闲时线程.start()
                                        break
                                else:
                                    print(f'未找到配置的闲时任务: {self.闲时任务}')
                                    当前闲时线程 = None

                    # 每分钟检查一次
                    time.sleep(60)

            except Exception as e:
                print(f'主函数异常: {e}')
                time.sleep(60)
        
    def __del__(self):
        if self.online_thread and self.online_thread._state != ThreadState.DESTROYED:
            self.online_thread.destroy()
        print('线程已销毁')


def function_test():
    config = {
        "服务器": "x12",
        "账号": "memorysiliao",
        "密码": "000000",
        "角色序号": "0",
        "代理配置": {
            "代理账号":"FQTUENGM",
            "代理密码":"35B7E37DAF97"
        },
        # 测试配置
        "日常拖把开关": True,
        "日常魔化开关": True,
        "活动如意开关": False,
        "活动通天开关": False,
        "活动逆无双开关": False,
        "活动罗汉开关": False,
        "闲时任务": "闲时_刷怪",
        "换装包前缀": "刷怪换装包",
        '卡级配置': 90,
        "通天配置": {
                "刷怪武器": "强击长矛",
                "职业技能": "霸王枪",
                "释放技能列表": ["猛抽", "致命一击", "强力打击[1级]"],
                },
        "逆无双配置": {
                "刷怪武器": "强击长矛",
                "职业技能": "霸王枪",
                "释放技能列表": ["猛抽", "致命一击", "强力打击[1级]"],
                },
        "刷怪配置": {
                "刷怪武器": "刺客之刃",
                "职业技能": "龙葵匕刃",
                "释放技能列表": ["强力打击[1级]","凿击[1级]", "猛抽", "致命一击", ],
                "刷怪起点": "map.kasitepingyuan|卡斯特平原34",
                "刷怪路线": "卡斯特平原34,卡斯特平原12,卡斯特平原15,卡斯特平原41,卡斯特平原19,卡斯特平原42,卡斯特平原21,卡斯特平原17,卡斯特平原18,卡斯特平原16,卡斯特平原36,卡斯特平原35",
                "刷怪目标": ["豆芽", "蚱蜢", "螃蟹"],
                "伏击技能": "伏击[一级]",
                "每圈结束执行的其他代码": None,
                "跑图多少圈进行一次售卖": 5,
                "售卖道具列表":["猎鹰之", "巨猿之", "野猪之","猛虎之", "达拉", "宝箱","民兵","针织","螃蟹","亚麻","普通","农夫","园艺","能量之","敏捷之","鼓舞","坚韧","精神","奥术","猛击","炸弹","战斗怒吼","暴怒","残忍","雄鹰之","灵猴之","巨鲸之"]
            },
        "技能配置":{
                    "出血[6级]":{"前摇":0,"后摇":0,"CD":5,"剩余CD": 0},
                    "冥火爪[11级]":{"前摇":0,"后摇":0,"CD":10,"剩余CD": 0},
                    "凿击[1级]":{"前摇":0,"后摇":0,"CD":20,"剩余CD": 0},
                    "强力打击[1级]":{"前摇":3,"后摇":0,"CD":6,"剩余CD": 0},
                    "猛抽":{"前摇":0,"后摇":0,"CD":30,"剩余CD": 0},
                    "致命一击":{"前摇":3,"后摇":0,"CD":40,"剩余CD": 0},
            },

    }

    print('开始测试...')
    myj = MYJ(config)
    while myj.登入标记 == False:
        time.sleep(1)
    局_测试线程 = MyThread(target=myj.主函数, name=f'测试-{myj.账号}')
    局_测试线程.start()
    time.sleep(9999)
    # 运行主函数
    # myj.主函数()
    # myj.活动_通天()
    del myj
    
if __name__ == '__main__':
    function_test()
    