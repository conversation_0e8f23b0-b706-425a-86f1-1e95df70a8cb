import datetime
import ntplib
import os
import mythread
import time

def get_time_str():
    return datetime.datetime.now().strftime('%H:%M:%S')

def auth_time():
    try:
        client = ntplib.NTPClient()
        # 尝试多个NTP服务器
        ntp_servers = ['pool.ntp.org', 'time.windows.com', 'time.nist.gov', 'cn.pool.ntp.org']
        for server in ntp_servers:
            try:
                response = client.request(server, timeout=5)
                timestamp = response.tx_time
                return datetime.datetime.fromtimestamp(timestamp)
            except:
                continue
        return 0
    except Exception as e:
        print(e)
        return 0

def check_expiry_and_exit(year, month, day):
    """
    检查授权到期时间
    参数:
        year (int): 年份
        month (int): 月份
        day (int): 日期
    """
    while True:
        当前时间 = auth_time()
        if 当前时间 == 0:
            print("授权时间获取失败，程序即将退出...")
            time.sleep(2)
            os._exit(1)

        # 检查日期是否超过指定日期
        # 如果年份超过指定年份，直接退出
        if 当前时间.year > year:
            print(f"授权已过期（当前日期：{当前时间.strftime('%Y-%m-%d')}，到期日期：{year}-{month:02d}-{day:02d}），程序即将退出...")
            time.sleep(2)
            os._exit(1)
        # 如果是指定年份，检查月份和日期
        elif 当前时间.year == year:
            if 当前时间.month > month or (当前时间.month == month and 当前时间.day > day):
                print(f"授权已过期（当前日期：{当前时间.strftime('%Y-%m-%d')}，到期日期：{year}-{month:02d}-{day:02d}），程序即将退出...")
                time.sleep(2)
                os._exit(1)

        # 每60秒检查一次
        time.sleep(10)
if __name__ == "__main__":
    print(auth_time())