import tkinter as tk
from tkinter import ttk
import threading
from typing import List, Dict, Any
from default import default_speed

class MYJMonitorUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("MYJ 多线程监控界面")
        self.root.geometry("1200x800")

        # 数据存储
        self.myj_objects = []  # MYJ对象列表
        self.selected_row = None  # 当前选中的行

        # 可配置的显示列
        self.display_columns = [
            "区服", "账号", "角色名", "等级", "百分比经验",
            "当前血量/总血量", "当前蓝量/总蓝量", "地图", "房间"
        ]

        self.setup_ui()

    def setup_ui(self):
        """设置UI界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 上半部分 - 表格
        self.setup_table_frame(main_frame)

        # 分隔线
        separator = ttk.Separator(main_frame, orient='horizontal')
        separator.pack(fill=tk.X, pady=10)

        # 下半部分 - 信息显示
        self.setup_info_frame(main_frame)

    def setup_table_frame(self, parent):
        """设置表格框架"""
        table_frame = ttk.Frame(parent)
        table_frame.pack(fill=tk.BOTH, expand=True)

        # 创建表格
        self.tree = ttk.Treeview(table_frame, columns=self.display_columns, show='headings', height=10)

        # 设置列标题
        for col in self.display_columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120, minwidth=80, anchor='center')

        # 添加滚动条
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # 布局
        self.tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)

        # 绑定选择事件
        self.tree.bind('<<TreeviewSelect>>', self.on_row_select)

    def setup_info_frame(self, parent):
        """设置信息显示框架"""
        info_frame = ttk.Frame(parent)
        info_frame.pack(fill=tk.BOTH, expand=True)

        # 选择器框架
        selector_frame = ttk.Frame(info_frame)
        selector_frame.pack(fill=tk.X, pady=(0, 10))

        # 4个选择器
        self.info_type = tk.StringVar(value="近十条命令数组")

        ttk.Label(selector_frame, text="信息类型:").pack(side=tk.LEFT, padx=(0, 10))

        info_types = ["近十条命令数组", "近一百条左下框信息", "近一百条右上框信息", "近一百条右下框信息"]
        self.info_combobox = ttk.Combobox(selector_frame, textvariable=self.info_type,
                                         values=info_types, state="readonly", width=20)
        self.info_combobox.pack(side=tk.LEFT)
        self.info_combobox.bind('<<ComboboxSelected>>', self.on_info_type_change)

        # 文本显示框架
        text_frame = ttk.Frame(info_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)

        # 多行文本框
        self.info_text = tk.Text(text_frame, state=tk.DISABLED, wrap=tk.WORD)

        # 滚动条
        text_scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.info_text.yview)
        self.info_text.configure(yscrollcommand=text_scrollbar.set)

        # 布局
        self.info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        text_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def on_row_select(self, event):
        """处理行选择事件"""
        selection = self.tree.selection()
        if selection:
            self.selected_row = selection[0]
            self.update_info_display()

    def on_info_type_change(self, event):
        """处理信息类型变更事件"""
        self.update_info_display()

    def update_info_display(self):
        """更新信息显示"""
        if self.selected_row is None:
            return

        try:
            # 获取选中行的索引
            row_index = self.tree.index(self.selected_row)
            if row_index >= len(self.myj_objects):
                return

            myj_obj = self.myj_objects[row_index]
            info_type = self.info_type.get()

            # 根据信息类型获取对应数据
            info_text = self.get_info_text(myj_obj, info_type)

            # 更新文本框
            self.info_text.config(state=tk.NORMAL)
            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(1.0, info_text)
            # 滚动到底部显示最新数据
            self.info_text.see(tk.END)
            self.info_text.config(state=tk.DISABLED)
        except tk.TclError:
            # 如果选中的行已被删除，清空选择
            self.selected_row = None
            self.info_text.config(state=tk.NORMAL)
            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(1.0, "请选择一个MYJ对象查看详细信息")
            self.info_text.config(state=tk.DISABLED)

    def get_info_text(self, myj_obj, info_type):
        """获取指定类型的信息文本"""
        # 根据实际的MYJ对象属性来获取数据
        if info_type == "近十条命令数组":
            commands = getattr(myj_obj, '近十条命令数组', [])
            if commands:
                return '\n'.join([f"{i+1}. {cmd}" for i, cmd in enumerate(commands)])
            else:
                return '暂无命令数据'
        elif info_type == "近一百条左下框信息":
            info_list = getattr(myj_obj, '近一百条左下框信息', [])
            if info_list:
                return '\n'.join(info_list)
            else:
                return '暂无左下框信息'
        elif info_type == "近一百条右上框信息":
            info_list = getattr(myj_obj, '近一百条右上框信息', [])
            if info_list:
                return '\n'.join(info_list)
            else:
                return '暂无右上框信息'
        elif info_type == "近一百条右下框信息":
            info_list = getattr(myj_obj, '近一百条右下框信息', [])
            if info_list:
                return '\n'.join(info_list)
            else:
                return '暂无右下框信息'
        else:
            return "未知信息类型"

    def add_myj_object(self, myj_obj):
        """添加MYJ对象到监控列表"""
        self.myj_objects.append(myj_obj)
        self.refresh_table()

    def remove_myj_object(self, index):
        """移除指定索引的MYJ对象"""
        if 0 <= index < len(self.myj_objects):
            del self.myj_objects[index]
            self.refresh_table()

    def refresh_table(self):
        """刷新表格显示"""
        # 清空现有数据
        for item in self.tree.get_children():
            self.tree.delete(item)

        # 添加新数据
        for myj_obj in self.myj_objects:
            values = self.get_row_values(myj_obj)
            self.tree.insert('', tk.END, values=values)

    def get_row_values(self, myj_obj):
        """获取MYJ对象的行显示值"""
        # 根据实际的MYJ对象属性来获取数据
        return [
            getattr(myj_obj, '服务器', '未知'),
            getattr(myj_obj, '账号', '未知'),
            getattr(myj_obj, '宠物名', '未知'),
            getattr(myj_obj, '等级', '0'),
            f"{getattr(myj_obj, '经验百分比', 0)}%",
            f"{getattr(myj_obj, 'hp_left', 0)}/{getattr(myj_obj, 'hp_max', 0)}",
            f"{getattr(myj_obj, 'sp_left', 0)}/{getattr(myj_obj, 'sp_max', 0)}",
            getattr(myj_obj, '地图', '未知'),
            getattr(myj_obj, '房间', '未知')
        ]

    def update_display_columns(self, new_columns):
        """更新显示列配置"""
        self.display_columns = new_columns
        # 重新设置表格
        self.tree.destroy()
        self.setup_table_frame(self.root.children['!frame'])
        self.refresh_table()

    def start_monitoring(self):
        """启动监控界面"""
        # 定期刷新数据
        self.refresh_data()
        # 使用default_speed作为刷新频率（转换为毫秒）
        refresh_interval = int(default_speed * 1000)
        self.root.after(refresh_interval, self.start_monitoring)

    def refresh_data(self):
        """刷新数据显示"""
        # 保存当前选中的行索引
        current_selection = None
        if self.selected_row:
            try:
                current_selection = self.tree.index(self.selected_row)
            except tk.TclError:
                self.selected_row = None

        # 刷新表格
        self.refresh_table()

        # 尝试恢复选中状态
        if current_selection is not None and current_selection < len(self.myj_objects):
            try:
                items = self.tree.get_children()
                if current_selection < len(items):
                    self.tree.selection_set(items[current_selection])
                    self.selected_row = items[current_selection]
            except tk.TclError:
                self.selected_row = None

        # 更新信息显示
        if self.selected_row:
            self.update_info_display()

    def run(self):
        """运行UI"""
        self.start_monitoring()
        self.root.mainloop()


# 示例MYJ类（用于测试）
class MockMYJ:
    def __init__(self, server, account, character_name):
        self.server = server
        self.account = account
        self.character_name = character_name
        self.level = "90"
        self.exp_percent = "85%"
        self.current_hp = 1000
        self.max_hp = 1200
        self.current_mp = 800
        self.max_mp = 1000
        self.current_map = "卡斯特平原"
        self.current_room = "卡斯特平原34"
        self.recent_commands = "最近命令:\n1. 移动到卡斯特平原34\n2. 攻击豆芽君\n3. 拾取物品"
        self.left_bottom_info = "左下框信息:\n经验获得: +100\n金币获得: +50"
        self.right_top_info = "右上框信息:\n血量: 1000/1200\n蓝量: 800/1000"
        self.right_bottom_info = "右下框信息:\n当前状态: 正常\n连接状态: 已连接"


if __name__ == "__main__":
    # 创建UI
    ui = MYJMonitorUI()

    # 添加示例数据
    ui.add_myj_object(MockMYJ("x12", "test1", "角色1"))
    ui.add_myj_object(MockMYJ("x39", "test2", "角色2"))
    ui.add_myj_object(MockMYJ("s69", "test3", "角色3"))

    # 运行UI
    ui.run()